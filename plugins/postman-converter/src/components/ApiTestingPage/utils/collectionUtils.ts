import { ApiCollection, ApiFolder, ApiRequest, Collection, HttpMethod } from '../../../types';
import { ErrorApi } from '@backstage/core-plugin-api';
import { PostmanConverterApi } from '../../../api';

/**
 * Helper function to convert backend Collection to frontend ApiCollection
 */
export const convertToApiCollection = async (
  collection: Collection,
  errorApi: ErrorApi,
  postmanConverterApi: PostmanConverterApi
): Promise<ApiCollection> => {
  try {
    // If the collection has content, parse it
    if (collection.content) {
      try {
        // Process collection
        const postmanCollection = JSON.parse(collection.content);

        // Create a new ApiCollection
        const apiCollection: ApiCollection = {
          id: collection.id,
          name: collection.name,
          description: collection.description || '',
          folders: [],
          requests: {},
          environments: [],
        };

        // Process items recursively
        const processItems = (items: any[], parentFolderId?: string) => {
          if (!items || !Array.isArray(items)) {
            return;
          }

          for (const item of items) {
            if (item.item && Array.isArray(item.item)) {
              // This is a folder
              const folderId = `folder_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

              // Create a new folder
              const folder: ApiFolder = {
                id: folderId,
                name: item.name || 'Unnamed Folder',
                folders: [],
                requests: [],
              };

              // Add to parent folder or root
              if (parentFolderId) {
                // Find the parent folder
                const findFolder = (folders: ApiFolder[], id: string): ApiFolder | undefined => {
                  for (const f of folders) {
                    if (f.id === id) {
                      return f;
                    }
                    const found = findFolder(f.folders, id);
                    if (found) {
                      return found;
                    }
                  }
                  return undefined;
                };

                const parentFolder = findFolder(apiCollection.folders, parentFolderId);
                if (parentFolder) {
                  parentFolder.folders.push(folder);
                }
              } else {
                // Add to root
                apiCollection.folders.push(folder);
              }

              // Process items in this folder after adding the folder to the tree
              processItems(item.item, folderId);
            } else if (item.request) {
              // This is a request
              const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

              // Convert Postman request to ApiRequest
              const apiRequest: ApiRequest = {
                id: requestId,
                name: item.name || 'Unnamed Request',
                method: (item.request.method || 'GET') as HttpMethod,
                url: typeof item.request.url === 'string'
                  ? item.request.url
                  : item.request.url?.raw || '',
                headers: [],
                params: [],
                body: {
                  mode: 'none',
                  enabled: true,
                },
                preRequestScript: '',
                testScript: '',
              };

              // Process headers - include all headers regardless of disabled status
              if (item.request.header && Array.isArray(item.request.header)) {
                apiRequest.headers = item.request.header.map((h: any) => ({
                  key: h.key || '',
                  value: h.value || '',
                  enabled: h.disabled !== true, // Set enabled to true if not explicitly disabled
                }));
              }

              // Process URL params if present - include all params regardless of disabled status
              if (typeof item.request.url === 'object' && item.request.url.query) {
                apiRequest.params = item.request.url.query.map((q: any) => ({
                  key: q.key || '',
                  value: q.value || '',
                  enabled: q.disabled !== true, // Set enabled to true if not explicitly disabled
                }));
              }

              // Process body - include body content regardless of disabled status
              if (item.request.body) {
                apiRequest.body.mode = item.request.body.mode || 'none';
                apiRequest.body.enabled = item.request.body.disabled !== true; // Set enabled to true if not explicitly disabled

                if (item.request.body.mode === 'raw' && item.request.body.raw) {
                  apiRequest.body.raw = item.request.body.raw;
                } else if (item.request.body.mode === 'formdata' && item.request.body.formdata) {
                  apiRequest.body.mode = 'form-data';
                  apiRequest.body.formData = item.request.body.formdata.map((fd: any) => ({
                    key: fd.key || '',
                    value: fd.value || '',
                    type: fd.type || 'text',
                  }));
                } else if (item.request.body.mode === 'urlencoded' && item.request.body.urlencoded) {
                  apiRequest.body.mode = 'urlencoded';
                  apiRequest.body.urlencoded = item.request.body.urlencoded.map((ue: any) => ({
                    key: ue.key || '',
                    value: ue.value || '',
                  }));
                }
              }

              // Process pre-request script if present
              if (item.event && Array.isArray(item.event)) {
                const preRequestEvent = item.event.find((e: any) => e.listen === 'prerequest');
                if (preRequestEvent && preRequestEvent.script && preRequestEvent.script.exec) {
                  // Join the script lines into a single string
                  apiRequest.preRequestScript = Array.isArray(preRequestEvent.script.exec)
                    ? preRequestEvent.script.exec.join('\n')
                    : preRequestEvent.script.exec;
                }

                // Process test script if present
                const testEvent = item.event.find((e: any) => e.listen === 'test');
                if (testEvent && testEvent.script && testEvent.script.exec) {
                  // Join the script lines into a single string
                  apiRequest.testScript = Array.isArray(testEvent.script.exec)
                    ? testEvent.script.exec.join('\n')
                    : testEvent.script.exec;
                }
              }

              // Add to requests map
              apiCollection.requests[requestId] = apiRequest;

              // Add to parent folder if specified
              if (parentFolderId) {
                // Find the parent folder
                const findFolder = (folders: ApiFolder[], id: string): ApiFolder | undefined => {
                  for (const f of folders) {
                    if (f.id === id) {
                      return f;
                    }
                    const found = findFolder(f.folders, id);
                    if (found) {
                      return found;
                    }
                  }
                  return undefined;
                };

                const parentFolder = findFolder(apiCollection.folders, parentFolderId);
                if (parentFolder) {
                  parentFolder.requests.push(requestId);
                }
              }
            }
          }
        };

        // Start processing from the root
        if (postmanCollection.item) {
          processItems(postmanCollection.item);
        }

        return apiCollection;
      } catch (error) {
        errorApi.post(error instanceof Error ? error : new Error(`Error parsing collection content: ${String(error)}`));
        // Return a basic collection if parsing fails
        return {
          id: collection.id,
          name: collection.name,
          description: collection.description || '',
          folders: [],
          requests: {},
          environments: [],
        };
      }
    } else {
      // If no content, get the collection with content
      try {
        const detailedCollection = await postmanConverterApi.getCollectionById(collection.id);
        return convertToApiCollection(detailedCollection, errorApi, postmanConverterApi);
      } catch (error) {
        errorApi.post(error instanceof Error ? error : new Error(`Error fetching detailed collection: ${String(error)}`));
        // Return a basic collection if fetching fails
        return {
          id: collection.id,
          name: collection.name,
          description: collection.description || '',
          folders: [],
          requests: {},
          environments: [],
        };
      }
    }
  } catch (error) {
    errorApi.post(error instanceof Error ? error : new Error(`Error converting collection: ${String(error)}`));
    // Return a basic collection if conversion fails
    return {
      id: collection.id,
      name: collection.name,
      description: collection.description || '',
      folders: [],
      requests: {},
      environments: [],
    };
  }
};
