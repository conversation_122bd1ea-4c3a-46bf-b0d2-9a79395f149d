import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  make<PERSON><PERSON><PERSON>,
  Button,
  Divider,

  Box,
  Chip,
  Paper,
  TextField,
  MenuItem,
  Select,
  FormControl,
  FormControlLabel,
  Checkbox,
  InputLabel,
  Tab,
  Tabs,
  IconButton,
  Tooltip,
  CircularProgress,
  Snackbar,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  ListItemIcon,
  Collapse,
  Menu,
  Badge,
} from '@material-ui/core';
import {
  InfoCard,
  EmptyState,
  CodeSnippet,
  ErrorPanel,
} from '@backstage/core-components';
import { Alert } from '@material-ui/lab';
import { useApi, errorApiRef } from '@backstage/core-plugin-api';
import { useAsync } from 'react-use';
import { ImportDialog } from './ImportDialog';
import { ExportDialog } from './ExportDialog';
import { TestGeneratorPanel } from './TestGeneratorPanel';
import { TestResultsPanel, TestResult } from './TestResultsPanel';
import { PreRequestScriptPanel } from './PreRequestScriptPanel';
import { CreateFolderDialog } from './CreateFolderDialog';
import { CreateRequestDialog } from './CreateRequestDialog';
import { RenameDialog } from './components/RenameDialog';
import { TabPanel } from './components/TabPanel';
import { RenderFolder } from './components/RenderFolder';

// Icons
import TestingIcon from '@material-ui/icons/BugReport';
import SendIcon from '@material-ui/icons/Send';
import AddIcon from '@material-ui/icons/Add';
import DeleteIcon from '@material-ui/icons/Delete';
import SettingsIcon from '@material-ui/icons/Settings';
import ImportExportIcon from '@material-ui/icons/ImportExport';
import CloseIcon from '@material-ui/icons/Close';
import FolderIcon from '@material-ui/icons/Folder';
import DescriptionIcon from '@material-ui/icons/Description';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import ChevronRightIcon from '@material-ui/icons/ChevronRight';
import MoreVertIcon from '@material-ui/icons/MoreVert';
import EditIcon from '@material-ui/icons/Edit';
import FileCopyIcon from '@material-ui/icons/FileCopy';
import SaveIcon from '@material-ui/icons/Save';

// Types
import {
  HttpMethod,
  ApiRequest,
  ApiResponse,
  ApiEnvironment,
  ApiCollection,
  ApiFolder,
  Collection
} from '../../types';
import { postmanConverterApiRef } from '../../api';
import { generateTestScript } from '../../utils/testGenerator';
import { executeTests } from '../../utils/testExecutor';
import { convertToApiCollection } from './utils/collectionUtils';

const useStyles = makeStyles(theme => ({
  root: {
    height: '100%',
  },
  infoCard: {
    marginBottom: theme.spacing(3),
  },
  paper: {
    padding: theme.spacing(2),
    height: '100%',
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120,
  },
  selectEmpty: {
    marginTop: theme.spacing(2),
  },
  urlBar: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
  },
  methodSelect: {
    width: 120,
    marginRight: theme.spacing(2),
  },
  urlField: {
    flexGrow: 1,
    marginRight: theme.spacing(2),
  },
  sendButton: {
    marginLeft: theme.spacing(1),
  },
  responsePanel: {
    marginTop: theme.spacing(2),
  },
  statusSuccess: {
    color: theme.palette.success.main,
  },
  statusError: {
    color: theme.palette.error.main,
  },
  statusInfo: {
    color: theme.palette.info.main,
  },
  statusWarning: {
    color: theme.palette.warning.main,
  },
  responseTime: {
    marginLeft: theme.spacing(2),
    fontSize: '0.875rem',
  },
  sidebarItem: {
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: theme.palette.action.hover,
    },
  },
  sidebarItemSelected: {
    backgroundColor: theme.palette.action.selected,
  },
  keyValueRow: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(1),
  },
  keyValueKey: {
    flexGrow: 1,
    marginRight: theme.spacing(1),
  },
  keyValueValue: {
    flexGrow: 2,
    marginRight: theme.spacing(1),
  },
  keyValueActions: {
    display: 'flex',
  },
  addButton: {
    marginTop: theme.spacing(1),
  },
  bodyEditor: {
    fontFamily: 'monospace',
    width: '100%',
    minHeight: '200px',
  },
  divider: {
    margin: theme.spacing(2, 0),
  },
  environmentSelector: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
  },
  envSelect: {
    flexGrow: 1,
    marginRight: theme.spacing(1),
  },
  envActions: {
    display: 'flex',
  },
  collectionTree: {
    maxHeight: 'calc(100vh - 300px)',
    overflow: 'auto',
  },
  loadingContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '200px',
  },
}));

// Helper function to create a new request
const createNewRequest = (): ApiRequest => ({
  id: `req_${Date.now()}`,
  name: 'New Request',
  method: 'GET',
  url: '',
  headers: [],
  params: [],
  body: {
    mode: 'none',
    enabled: true,
  },
  preRequestScript: '',
  testScript: '',
});

// Helper function to create a new environment
const createNewEnvironment = (): ApiEnvironment => ({
  id: `env_${Date.now()}`,
  name: 'New Environment',
  variables: [],
});

// Helper function to convert ApiCollection back to Postman collection format
const convertApiCollectionToPostman = (apiCollection: ApiCollection): any => {
  const postmanCollection = {
    info: {
      name: apiCollection.name,
      description: apiCollection.description,
      schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
    },
    item: [] as any[]
  };

  // Helper function to convert ApiRequest to Postman request
  const convertRequestToPostman = (request: ApiRequest): any => {
    const postmanRequest: any = {
      name: request.name,
      request: {
        method: request.method,
        header: request.headers.map(h => ({
          key: h.key,
          value: h.value,
          disabled: !h.enabled
        })),
        url: {
          raw: request.url,
          query: request.params.map(p => ({
            key: p.key,
            value: p.value,
            disabled: !p.enabled
          }))
        }
      }
    };

    // Add body if present
    if (request.body.mode !== 'none') {
      postmanRequest.request.body = {
        mode: request.body.mode,
        disabled: !request.body.enabled
      };

      if (request.body.mode === 'raw' && request.body.raw) {
        postmanRequest.request.body.raw = request.body.raw;
      } else if (request.body.mode === 'form-data' && request.body.formData) {
        postmanRequest.request.body.formdata = request.body.formData.map(fd => ({
          key: fd.key,
          value: fd.value,
          type: fd.type
        }));
      } else if (request.body.mode === 'urlencoded' && request.body.urlencoded) {
        postmanRequest.request.body.urlencoded = request.body.urlencoded.map(ue => ({
          key: ue.key,
          value: ue.value
        }));
      }
    }

    // Add events (scripts)
    const events = [];
    if (request.preRequestScript) {
      events.push({
        listen: 'prerequest',
        script: {
          exec: request.preRequestScript.split('\n'),
          type: 'text/javascript'
        }
      });
    }
    if (request.testScript) {
      events.push({
        listen: 'test',
        script: {
          exec: request.testScript.split('\n'),
          type: 'text/javascript'
        }
      });
    }
    if (events.length > 0) {
      postmanRequest.event = events;
    }

    return postmanRequest;
  };

  // Helper function to convert ApiFolder to Postman folder
  const convertFolderToPostman = (folder: ApiFolder): any => {
    const postmanFolder = {
      name: folder.name,
      item: [] as any[]
    };

    // Add requests in this folder
    folder.requests.forEach(requestId => {
      const request = apiCollection.requests[requestId];
      if (request) {
        postmanFolder.item.push(convertRequestToPostman(request));
      }
    });

    // Add nested folders
    folder.folders.forEach(nestedFolder => {
      postmanFolder.item.push(convertFolderToPostman(nestedFolder));
    });

    return postmanFolder;
  };

  // Add root-level folders
  apiCollection.folders.forEach(folder => {
    postmanCollection.item.push(convertFolderToPostman(folder));
  });

  // Add orphaned requests (requests not in any folder)
  if (apiCollection._orphanedRequests) {
    apiCollection._orphanedRequests.forEach(requestId => {
      const request = apiCollection.requests[requestId];
      if (request) {
        postmanCollection.item.push(convertRequestToPostman(request));
      }
    });
  }

  return postmanCollection;
};

// Helper function to replace environment variables in a string
const resolveVariables = (
  text: string,
  environment?: ApiEnvironment
): string => {
  if (!text || !environment) return text || '';

  let result = text;

  // Replace {{variable}} with the value from the environment
  const variableRegex = /\{\{([^}]+)\}\}/g;
  let match = variableRegex.exec(text);

  while (match !== null) {
    const variableName = match[1];
    const variable = environment.variables.find(
      v => v.key === variableName && v.enabled
    );

    if (variable) {
      result = result.replace(`{{${variableName}}}`, variable.value);
    }

    match = variableRegex.exec(text);
  }

  return result;
};

// Function to send a request directly without using the Backstage proxy
const sendRequest = async (
  request: ApiRequest,
  environment?: ApiEnvironment
): Promise<ApiResponse> => {
  try {
    // Resolve environment variables in URL
    const resolvedUrl = resolveVariables(request.url, environment);

    // Validate the URL
    let url: URL;
    try {
      url = new URL(resolvedUrl);
    } catch (error) {
      throw new Error(`Invalid URL: ${resolvedUrl}. Please provide a valid URL including protocol (e.g., https://)`);
    }

    // Process headers with environment variables
    const headers: Record<string, string> = {};
    request.headers
      .filter(header => header.enabled)
      .forEach(header => {
        const key = resolveVariables(header.key, environment);
        const value = resolveVariables(header.value, environment);
        headers[key] = value;
      });

    // Process authentication if present
    if (request.auth) {
      switch (request.auth.type) {
        case 'basic':
          if (request.auth.basic) {
            const username = resolveVariables(request.auth.basic.username, environment);
            const password = resolveVariables(request.auth.basic.password, environment);
            const base64Credentials = btoa(`${username}:${password}`);
            headers.Authorization = `Basic ${base64Credentials}`;
          }
          break;
        case 'bearer':
          if (request.auth.bearer) {
            const token = resolveVariables(request.auth.bearer.token, environment);
            headers.Authorization = `Bearer ${token}`;
          }
          break;
        case 'apiKey':
          if (request.auth.apiKey) {
            const key = resolveVariables(request.auth.apiKey.key, environment);
            const value = resolveVariables(request.auth.apiKey.value, environment);
            if (request.auth.apiKey.in === 'header') {
              headers[key] = value;
            } else if (request.auth.apiKey.in === 'query') {
              // Will be handled in query parameters
            }
          }
          break;
        default:
          // No authentication or 'none' type - no action needed
          break;
      }
    }

    // Process query parameters with environment variables
    const queryParams = new URLSearchParams();
    request.params
      .filter(param => param.enabled)
      .forEach(param => {
        const key = resolveVariables(param.key, environment);
        const value = resolveVariables(param.value, environment);
        queryParams.append(key, value);
      });

    // Add API key to query parameters if needed
    if (request.auth?.type === 'apiKey' && request.auth.apiKey?.in === 'query') {
      const key = resolveVariables(request.auth.apiKey.key, environment);
      const value = resolveVariables(request.auth.apiKey.value, environment);
      queryParams.append(key, value);
    }

    // Add query parameters to URL
    queryParams.forEach((value, key) => {
      url.searchParams.append(key, value);
    });

    // Process request body with environment variables
    let processedBody: string | FormData | URLSearchParams | null = null;

    // Only process body if it's enabled (default to true if not specified)
    const bodyEnabled = request.body.enabled !== false;

    if (bodyEnabled) {
      if (request.body.mode === 'raw' && request.body.raw) {
        processedBody = resolveVariables(request.body.raw, environment);
      } else if (request.body.mode === 'urlencoded' && request.body.urlencoded) {
        const urlencoded = new URLSearchParams();
        request.body.urlencoded.forEach(item => {
          const key = resolveVariables(item.key, environment);
          const value = resolveVariables(item.value, environment);
          urlencoded.append(key, value);
        });
        processedBody = urlencoded;
      } else if (request.body.mode === 'form-data' && request.body.formData) {
        const formData = new FormData();
        request.body.formData.forEach(item => {
          const key = resolveVariables(item.key, environment);
          const value = resolveVariables(item.value, environment);
          formData.append(key, value);
        });
        processedBody = formData;
      }
    }

    // Prepare the fetch options
    const fetchOptions: RequestInit = {
      method: request.method,
      headers: headers,
      mode: 'cors', // Enable CORS for cross-origin requests
    };

    // Add the body if it's not a GET or HEAD request
    if (request.method !== 'GET' && request.method !== 'HEAD' && processedBody) {
      fetchOptions.body = processedBody;
    }

    // Record the start time
    const startTime = performance.now();

    // Send the request directly to the target URL
    const response = await fetch(url.toString(), fetchOptions);

    // Record the end time
    const endTime = performance.now();
    const responseTime = Math.round(endTime - startTime);

    // Get the response headers
    const responseHeaders: Record<string, string> = {};
    response.headers.forEach((value, key) => {
      responseHeaders[key] = value;
    });

    // Get the response body
    let responseBody = '';
    let responseSize = 0;

    try {
      // Try to get the response as text
      const responseText = await response.text();
      responseBody = responseText;
      responseSize = new Blob([responseText]).size;

      // If the content type is JSON, try to format it
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        try {
          const jsonObj = JSON.parse(responseText);
          responseBody = JSON.stringify(jsonObj, null, 2);
        } catch (e) {
          // If JSON parsing fails, just use the raw text
          // Silently ignore JSON parsing errors
        }
      }
    } catch (e) {
      responseBody = `Failed to read response body: ${String(e)}`;
    }

    // Create the response object
    return {
      status: response.status,
      statusText: response.statusText,
      headers: responseHeaders,
      body: responseBody,
      time: responseTime,
      size: responseSize,
      url: url.toString(),
    };
  } catch (error) {
    // Re-throw the error to be handled by the caller
    throw error;
  }
};





export const ApiTestingPage = () => {
  const classes = useStyles();
  const errorApi = useApi(errorApiRef);
  const postmanConverterApi = useApi(postmanConverterApiRef);

  // State for the current request
  const [currentRequest, setCurrentRequest] = useState<ApiRequest>(createNewRequest());

  // State for the current response
  const [currentResponse, setCurrentResponse] = useState<ApiResponse | null>(null);

  // State for loading indicator
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // State for request form tabs
  const [tabValue, setTabValue] = useState(0);

  // State for response tabs
  const [responseTabValue, setResponseTabValue] = useState(0);

  // State for test-related functionality
  const [isGeneratingTests, setIsGeneratingTests] = useState<boolean>(false);
  const [isRunningTests, setIsRunningTests] = useState<boolean>(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [testError, setTestError] = useState<string | null>(null);

  // State for pre-request script functionality
  const [isSavingPreRequestScript, setIsSavingPreRequestScript] = useState<boolean>(false);
  const [preRequestScriptError, setPreRequestScriptError] = useState<string | null>(null);

  // State for environments
  const [environments, setEnvironments] = useState<ApiEnvironment[]>([createNewEnvironment()]);
  const [currentEnvironment, setCurrentEnvironment] = useState<string>(environments[0].id);
  const [environmentDialogOpen, setEnvironmentDialogOpen] = useState<boolean>(false);
  const [selectedEnvironmentId, setSelectedEnvironmentId] = useState<string>(environments[0].id);

  // Computed selected environment
  const selectedEnvironment = environments.find(env => env.id === selectedEnvironmentId);

  // State for collections
  const [collections, setCollections] = useState<ApiCollection[]>([]);

  // State for expanded folders - initialize as empty and expand folders when collections are loaded
  const [expandedFolders, setExpandedFolders] = useState<Record<string, boolean>>({});

  // State for tracking unsaved changes
  const [unsavedCollections, setUnsavedCollections] = useState<Set<string>>(new Set());
  const [isSaving, setIsSaving] = useState<Record<string, boolean>>({});

  // Helper function to mark a collection as having unsaved changes
  const markCollectionAsUnsaved = (collectionId: string) => {
    setUnsavedCollections(prev => new Set([...prev, collectionId]));
  };

  // Helper function to mark a collection as saved
  const markCollectionAsSaved = (collectionId: string) => {
    setUnsavedCollections(prev => {
      const newSet = new Set(prev);
      newSet.delete(collectionId);
      return newSet;
    });
  };

  // Helper function to get method color
  const getMethodColor = (method: HttpMethod): string => {
    switch (method) {
      case 'GET': return '#61affe';
      case 'POST': return '#49cc90';
      case 'PUT': return '#fca130';
      case 'DELETE': return '#f93e3e';
      case 'PATCH': return '#50e3c2';
      case 'HEAD': return '#9012fe';
      case 'OPTIONS': return '#0d5aa7';
      default: return '#6c757d';
    }
  };

  // State for selected collection item
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);

  // State for context menu
  const [contextMenu, setContextMenu] = useState<{
    mouseX: number;
    mouseY: number;
    itemId: string;
    itemType: 'collection' | 'folder' | 'request';
  } | null>(null);

  // State for edit collection dialog
  const [editDialogOpen, setEditDialogOpen] = useState<boolean>(false);
  const [editCollectionId, setEditCollectionId] = useState<string>('');
  const [editCollectionName, setEditCollectionName] = useState<string>('');
  const [editCollectionDescription, setEditCollectionDescription] = useState<string>('');

  // State for import/export dialogs
  const [importDialogOpen, setImportDialogOpen] = useState<boolean>(false);
  const [exportDialogOpen, setExportDialogOpen] = useState<boolean>(false);

  // State for snackbar
  const [snackbar, setSnackbar] = useState<{open: boolean, message: string, severity: 'success' | 'error' | 'info' | 'warning'}>({
    open: false,
    message: '',
    severity: 'info',
  });



  // Fetch collections from the API
  const { loading: collectionsLoading, error: collectionsError } = useAsync(async () => {
    try {
      const fetchedCollections = await postmanConverterApi.getCollections();

      // Convert backend collections to frontend ApiCollection format
      const apiCollections: ApiCollection[] = [];

      for (const collection of fetchedCollections) {
        const apiCollection = await convertToApiCollection(collection, errorApi, postmanConverterApi);
        apiCollections.push(apiCollection);
      }

      // Auto-expand all folders and collections for better user experience
      if (apiCollections.length > 0) {
        const expandedState: Record<string, boolean> = {...expandedFolders};

        apiCollections.forEach(collection => {
          // Mark the collection itself as expanded
          expandedState[`collection_${collection.id}`] = true;

          // Expand all folders
          const expandAllFolders = (folders: ApiFolder[]) => {
            folders.forEach(folder => {
              expandedState[folder.id] = true;
              if (folder.folders.length > 0) {
                expandAllFolders(folder.folders);
              }
            });
          };

          expandAllFolders(collection.folders);
        });

        setExpandedFolders(expandedState);
        setCollections(apiCollections);
      }

      return fetchedCollections;
    } catch (e) {
      const error = e instanceof Error ? e : new Error(String(e));
      errorApi.post(error);
      throw error;
    }
  }, []);

  // Handle tab change
  const handleTabChange = (_event: React.ChangeEvent<{}>, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle test generation
  const handleGenerateTests = () => {
    if (!currentResponse) return;

    setIsGeneratingTests(true);
    setTestError(null);

    try {
      // Parse the response body as JSON if possible
      let responseData = null;
      try {
        if (currentResponse.body &&
            currentResponse.headers &&
            currentResponse.headers['content-type'] &&
            currentResponse.headers['content-type'].includes('application/json')) {
          responseData = JSON.parse(currentResponse.body);
        }
      } catch (error) {
        // Silently handle JSON parsing errors
        responseData = null;
      }

      // Generate test script using the utility function
      const testScript = generateTestScript({
        status: currentResponse.status,
        headers: currentResponse.headers,
        data: responseData
      });

      // Update the current request with the test script
      setCurrentRequest(prev => ({
        ...prev,
        testScript: testScript
      }));

      // If this request is part of a collection, update it there too
      if (selectedItemId) {
        const updatedCollections = collections.map(collection => {
          if (collection.requests[selectedItemId]) {
            const updatedRequests = {
              ...collection.requests,
              [selectedItemId]: {
                ...collection.requests[selectedItemId],
                testScript: testScript
              }
            };

            return {
              ...collection,
              requests: updatedRequests
            };
          }
          return collection;
        });

        setCollections(updatedCollections);
      }

      setSnackbar({
        open: true,
        message: 'Test script generated successfully',
        severity: 'success',
      });
    } catch (error) {
      setTestError(error instanceof Error ? error.message : String(error));
      setSnackbar({
        open: true,
        message: 'Failed to generate test script',
        severity: 'error',
      });
    } finally {
      setIsGeneratingTests(false);
    }
  };

  // Handle running tests
  const handleRunTests = async (testScript: string) => {
    if (!currentResponse || !testScript) return;

    setIsRunningTests(true);
    setTestError(null);
    setTestResults([]);

    try {
      // Use the real test executor to run the tests
      const executorResults = executeTests(currentResponse, testScript);

      // Convert executor results to the format expected by the UI
      const results: TestResult[] = executorResults.map(result => ({
        id: result.id || `test-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        name: result.name,
        passed: result.passed,
        error: result.error,
        duration: result.duration || 0
      }));

      setTestResults(results);

      // Update the current request with the test results
      setCurrentRequest(prev => ({
        ...prev,
        lastTestResults: results
      }));

      // If this request is part of a collection, update it there too
      if (selectedItemId) {
        const updatedCollections = collections.map(collection => {
          if (collection.requests[selectedItemId]) {
            const updatedRequests = {
              ...collection.requests,
              [selectedItemId]: {
                ...collection.requests[selectedItemId],
                lastTestResults: results
              }
            };

            return {
              ...collection,
              requests: updatedRequests
            };
          }
          return collection;
        });

        setCollections(updatedCollections);
      }

      const passedCount = results.filter(r => r.passed).length;
      setSnackbar({
        open: true,
        message: `Tests completed: ${passedCount}/${results.length} passed`,
        severity: passedCount === results.length ? 'success' : 'warning',
      });
    } catch (error) {
      setTestError(error instanceof Error ? error.message : String(error));
      setSnackbar({
        open: true,
        message: 'Failed to run tests',
        severity: 'error',
      });
    } finally {
      setIsRunningTests(false);
    }
  };

  // Handle saving tests
  const handleSaveTests = (testScript: string) => {
    // Update the current request with the test script
    setCurrentRequest(prev => ({
      ...prev,
      testScript: testScript
    }));

    // If this request is part of a collection, update it there too
    if (selectedItemId) {
      const updatedCollections = collections.map(collection => {
        if (collection.requests[selectedItemId]) {
          const updatedRequests = {
            ...collection.requests,
            [selectedItemId]: {
              ...collection.requests[selectedItemId],
              testScript: testScript
            }
          };

          return {
            ...collection,
            requests: updatedRequests
          };
        }
        return collection;
      });

      setCollections(updatedCollections);

      // Mark collection as having unsaved changes
      const collection = collections.find(c => c.requests[selectedItemId]);
      if (collection) {
        markCollectionAsUnsaved(collection.id);
      }
    }

    setSnackbar({
      open: true,
      message: 'Test script saved successfully',
      severity: 'success',
    });
  };

  // Handle saving pre-request scripts
  const handleSavePreRequestScript = (script: string) => {
    setIsSavingPreRequestScript(true);
    setPreRequestScriptError(null);

    try {
      // Update the current request with the pre-request script
      setCurrentRequest(prev => ({
        ...prev,
        preRequestScript: script
      }));

      // If this request is part of a collection, update it there too
      if (selectedItemId) {
        const updatedCollections = collections.map(collection => {
          if (collection.requests[selectedItemId]) {
            const updatedRequests = {
              ...collection.requests,
              [selectedItemId]: {
                ...collection.requests[selectedItemId],
                preRequestScript: script
              }
            };

            return {
              ...collection,
              requests: updatedRequests
            };
          }
          return collection;
        });

        setCollections(updatedCollections);

        // Mark collection as having unsaved changes
        const collection = collections.find(c => c.requests[selectedItemId]);
        if (collection) {
          markCollectionAsUnsaved(collection.id);
        }
      }

      setSnackbar({
        open: true,
        message: 'Pre-request script saved successfully',
        severity: 'success',
      });
    } catch (error) {
      setPreRequestScriptError(error instanceof Error ? error.message : String(error));
      setSnackbar({
        open: true,
        message: 'Failed to save pre-request script',
        severity: 'error',
      });
    } finally {
      setIsSavingPreRequestScript(false);
    }
  };

  // Handle method change
  const handleMethodChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setCurrentRequest({
      ...currentRequest,
      method: event.target.value as HttpMethod,
    });
  };

  // Handle URL change
  const handleUrlChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setCurrentRequest({
      ...currentRequest,
      url: event.target.value,
    });
  };

  // Get helper text for URL field
  const getUrlHelperText = (url: string): string => {
    if (!url) {
      return "Enter a URL to send a request";
    }

    // Direct request without proxy
    return "Request will be sent directly to the target URL";
  };

  // Handle send request
  const handleSendRequest = async () => {
    setIsLoading(true);
    setCurrentResponse(null);
    setResponseTabValue(0); // Reset to body tab
    setTestResults([]); // Clear test results

    try {
      // Get the current environment
      const currentEnv = environments.find(env => env.id === currentEnvironment);

      // Use the sendRequest function to send the request directly
      const apiResponse = await sendRequest(currentRequest, currentEnv);

      setCurrentResponse(apiResponse);
      setSnackbar({
        open: true,
        message: 'Request sent successfully',
        severity: 'success',
      });
    } catch (error) {
      // Determine the error type and provide a more specific error message
      let errorMessage = 'Failed to send request';
      const errorDetails = error instanceof Error ? error.message : String(error);

      if (error instanceof TypeError && errorDetails.includes('Failed to fetch')) {
        errorMessage = 'Network error: Unable to connect to the server';
      } else if (error instanceof TypeError && errorDetails.includes('Invalid URL')) {
        errorMessage = 'Invalid URL: Please check the request URL';
      } else if (errorDetails.includes('Invalid URL')) {
        errorMessage = errorDetails; // Use the custom error message for invalid URLs
      } else if (error instanceof SyntaxError) {
        errorMessage = 'Syntax error in request or response';
      }

      // Create a detailed error response
      setCurrentResponse({
        status: 0,
        statusText: 'Error',
        headers: {},
        body: JSON.stringify({
          error: errorMessage,
          details: errorDetails,
          request: {
            url: currentRequest.url,
            method: currentRequest.method,
          },
        }, null, 2),
        time: 0,
        size: 0,
        error: errorDetails,
      });

      setSnackbar({
        open: true,
        message: errorMessage,
        severity: 'error',
      });

      // Log the error to the error API
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
    } finally {
      setIsLoading(false);
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({
      ...snackbar,
      open: false,
    });
  };

  // Handle environment change
  const handleEnvironmentChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setCurrentEnvironment(event.target.value as string);
  };

  // Handle import/export dialog close
  const handleCloseImportDialog = () => {
    setImportDialogOpen(false);
  };

  const handleOpenExportDialog = () => {
    setExportDialogOpen(true);
  };

  const handleCloseExportDialog = () => {
    setExportDialogOpen(false);
  };

  const handleImportCollection = (apiCollection: ApiCollection) => {
    // Convert ApiCollection to Postman collection format
    const postmanCollection = {
      info: {
        name: apiCollection.name,
        description: apiCollection.description,
        schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
      },
      item: [] // We would need to convert the folders and requests structure here
    };

    // Save to database
    postmanConverterApi.createCollection({
      name: apiCollection.name,
      description: apiCollection.description,
      content: JSON.stringify(postmanCollection)
    }).then(async newCollection => {
      // Convert back to ApiCollection format
      const savedApiCollection = await convertToApiCollection(newCollection, errorApi, postmanConverterApi);

      // Add to collections state
      setCollections(prevCollections => [...prevCollections, savedApiCollection]);

      setSnackbar({
        open: true,
        message: `Collection "${apiCollection.name}" imported successfully`,
        severity: 'success',
      });
    }).catch(error => {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      setSnackbar({
        open: true,
        message: 'Failed to import collection',
        severity: 'error',
      });
    });
  };

  const handleImportEnvironment = (environment: ApiEnvironment) => {
    setEnvironments([...environments, environment]);
    setSnackbar({
      open: true,
      message: `Environment "${environment.name}" imported successfully`,
      severity: 'success',
    });
  };

  // Handle folder toggle
  const handleFolderToggle = (folderId: string) => {
    setExpandedFolders({
      ...expandedFolders,
      [folderId]: !expandedFolders[folderId],
    });
  };

  // Handle item selection
  const handleItemSelect = (itemId: string) => {
    setSelectedItemId(itemId);

    // If it's a request, load it into the current request
    const collection = collections.find(col => {
      return Object.keys(col.requests).includes(itemId);
    });

    if (collection && collection.requests[itemId]) {
      setCurrentRequest(collection.requests[itemId]);
    }
  };

  // Handle context menu open
  const handleContextMenu = (
    event: React.MouseEvent,
    itemId: string,
    itemType: 'collection' | 'folder' | 'request'
  ) => {
    event.preventDefault();
    event.stopPropagation();

    setContextMenu({
      mouseX: event.clientX - 2,
      mouseY: event.clientY - 4,
      itemId,
      itemType,
    });
  };

  // Handle context menu close
  const handleContextMenuClose = () => {
    setContextMenu(null);
  };

  // Handle saving a collection to the database
  const handleSaveCollection = async (collectionId: string) => {
    const collection = collections.find(c => c.id === collectionId);
    if (!collection) return;

    setIsSaving(prev => ({ ...prev, [collectionId]: true }));

    try {
      // Convert ApiCollection back to Postman format
      const postmanCollection = convertApiCollectionToPostman(collection);

      // Update the collection in the database
      await postmanConverterApi.updateCollection(collectionId, {
        content: JSON.stringify(postmanCollection)
      });

      // Mark as saved
      markCollectionAsSaved(collectionId);

      setSnackbar({
        open: true,
        message: `Collection "${collection.name}" saved successfully`,
        severity: 'success',
      });
    } catch (error) {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      setSnackbar({
        open: true,
        message: `Failed to save collection "${collection.name}"`,
        severity: 'error',
      });
    } finally {
      setIsSaving(prev => ({ ...prev, [collectionId]: false }));
    }
  };

  // Handle add collection
  const handleAddCollection = () => {
    // Create a basic empty Postman collection structure
    const emptyCollection = {
      info: {
        name: 'New Collection',
        description: 'A new collection',
        schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
      },
      item: []
    };

    // Create a new collection in the database
    postmanConverterApi.createCollection({
      name: 'New Collection',
      description: 'A new collection',
      content: JSON.stringify(emptyCollection)
    }).then(async newCollection => {
      // Convert to ApiCollection format
      const apiCollection = await convertToApiCollection(newCollection, errorApi, postmanConverterApi);

      // Add to collections state
      setCollections(prevCollections => [...prevCollections, apiCollection]);

      setSnackbar({
        open: true,
        message: 'Collection created successfully',
        severity: 'success',
      });
    }).catch(error => {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      setSnackbar({
        open: true,
        message: 'Failed to create collection',
        severity: 'error',
      });
    });
  };

  // State for dialogs
  const [createFolderDialogOpen, setCreateFolderDialogOpen] = useState(false);
  const [createRequestDialogOpen, setCreateRequestDialogOpen] = useState(false);
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  const [renameDialogData, setRenameDialogData] = useState<{
    itemType: 'collection' | 'folder' | 'request';
    itemId: string;
    collectionId: string;
    currentName: string;
  } | null>(null);
  const [selectedCollectionForAction, setSelectedCollectionForAction] = useState<string>('');
  const [selectedFolderForAction, setSelectedFolderForAction] = useState<string>('');

  // State for edit dialog (collection dialog is handled separately)

  // Handle edit collection
  const handleEditCollection = (collectionId: string) => {
    // Find the collection to edit
    const collection = collections.find(col => col.id === collectionId);

    if (collection) {
      // Set up the edit dialog
      setEditCollectionId(collectionId);
      setEditCollectionName(collection.name);
      setEditCollectionDescription(collection.description);
      setEditDialogOpen(true);
    }

    handleContextMenuClose();
  };

  // Handle save edited collection
  const handleSaveEditedCollection = () => {
    // Update the collection in the database
    postmanConverterApi.updateCollection(editCollectionId, {
      name: editCollectionName,
      description: editCollectionDescription
    }).then(() => {
      // Update collections state
      setCollections(prevCollections =>
        prevCollections.map(col =>
          col.id === editCollectionId
            ? { ...col, name: editCollectionName, description: editCollectionDescription }
            : col
        )
      );

      setSnackbar({
        open: true,
        message: 'Collection updated successfully',
        severity: 'success',
      });

      // Close the dialog
      setEditDialogOpen(false);
    }).catch(error => {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      setSnackbar({
        open: true,
        message: 'Failed to update collection',
        severity: 'error',
      });
    });
  };

  // Handle duplicate request
  const handleDuplicateRequest = (requestId: string) => {
    const collection = collections.find(col => {
      return Object.keys(col.requests).includes(requestId);
    });

    if (collection) {
      const originalRequest = collection.requests[requestId];
      const newRequestId = `req_${Date.now()}`;

      // Create a copy of the request with a new ID
      const newRequest = {
        ...originalRequest,
        id: newRequestId,
        name: `${originalRequest.name} (Copy)`,
      };

      // Find the folder that contains the request
      const findFolderWithRequest = (folders: ApiFolder[], reqId: string): string | undefined => {
        for (const folder of folders) {
          if (folder.requests.includes(reqId)) {
            return folder.id;
          }

          if (folder.folders.length > 0) {
            const nestedResult = findFolderWithRequest(folder.folders, reqId);
            if (nestedResult) {
              return nestedResult;
            }
          }
        }

        return undefined;
      };

      const targetFolderId = findFolderWithRequest(collection.folders, requestId);

      // Update the collection
      const updatedCollections = collections.map(col => {
        if (col.id === collection.id) {
          // Add the new request to the requests object
          const updatedRequests = {
            ...col.requests,
            [newRequestId]: newRequest,
          };

          // If the request is in a folder, add it to that folder
          let updatedFolders = [...col.folders];

          if (targetFolderId) {
            updatedFolders = updatedFolders.map(folder => {
              if (folder.id === targetFolderId) {
                return {
                  ...folder,
                  requests: [...folder.requests, newRequestId],
                };
              }

              // Check nested folders
              if (folder.folders.length > 0) {
                const updateNestedFolders = (folders: ApiFolder[]): ApiFolder[] => {
                  return folders.map(nestedFolder => {
                    if (nestedFolder.id === targetFolderId) {
                      return {
                        ...nestedFolder,
                        requests: [...nestedFolder.requests, newRequestId],
                      };
                    }

                    if (nestedFolder.folders.length > 0) {
                      return {
                        ...nestedFolder,
                        folders: updateNestedFolders(nestedFolder.folders),
                      };
                    }

                    return nestedFolder;
                  });
                };

                return {
                  ...folder,
                  folders: updateNestedFolders(folder.folders),
                };
              }

              return folder;
            });
          }

          return {
            ...col,
            requests: updatedRequests,
            folders: updatedFolders,
          };
        }

        return col;
      });

      setCollections(updatedCollections);

      // Mark collection as having unsaved changes
      markCollectionAsUnsaved(collection.id);

      setSnackbar({
        open: true,
        message: 'Request duplicated successfully',
        severity: 'success',
      });
    }

    handleContextMenuClose();
  };

  // Handle delete item
  const handleDeleteItem = (itemId: string, itemType: 'collection' | 'folder' | 'request') => {
    if (itemType === 'collection') {
      // Delete collection from the database
      postmanConverterApi.deleteCollection(itemId)
        .then(() => {
          // Remove from local state
          setCollections(collections.filter(col => col.id !== itemId));

          setSnackbar({
            open: true,
            message: 'Collection deleted successfully',
            severity: 'success',
          });
        })
        .catch(error => {
          errorApi.post(error instanceof Error ? error : new Error(String(error)));
          setSnackbar({
            open: true,
            message: 'Failed to delete collection',
            severity: 'error',
          });
        });
    } else if (itemType === 'folder') {
      // Find the collection that contains this folder
      const collection = collections.find(col => {
        // Check if it's a top-level folder
        if (col.folders.some(folder => folder.id === itemId)) {
          return true;
        }

        // Check if it's a nested folder
        const hasNestedFolder = (folders: ApiFolder[]): boolean => {
          for (const folder of folders) {
            if (folder.id === itemId) {
              return true;
            }

            if (folder.folders.length > 0 && hasNestedFolder(folder.folders)) {
              return true;
            }
          }

          return false;
        };

        return hasNestedFolder(col.folders);
      });

      if (collection) {
        // Delete folder from local state
        const updatedCollections = collections.map(col => {
          if (col.id !== collection.id) {
            return col;
          }

          // Check if the folder is a top-level folder
          const topLevelFolderIndex = col.folders.findIndex(folder => folder.id === itemId);

          if (topLevelFolderIndex !== -1) {
            // It's a top-level folder
            const updatedFolders = [...col.folders];
            updatedFolders.splice(topLevelFolderIndex, 1);

            return {
              ...col,
              folders: updatedFolders,
            };
          }

          // Check if it's a nested folder
          const updateNestedFolders = (folders: ApiFolder[]): ApiFolder[] => {
            return folders.map(folder => {
              // Check if this folder contains the target folder
              const nestedFolderIndex = folder.folders.findIndex(nestedFolder => nestedFolder.id === itemId);

              if (nestedFolderIndex !== -1) {
                // Found the folder to delete
                const updatedNestedFolders = [...folder.folders];
                updatedNestedFolders.splice(nestedFolderIndex, 1);

                return {
                  ...folder,
                  folders: updatedNestedFolders,
                };
              }

              // Recursively check deeper nested folders
              if (folder.folders.length > 0) {
                return {
                  ...folder,
                  folders: updateNestedFolders(folder.folders),
                };
              }

              return folder;
            });
          };

          return {
            ...col,
            folders: updateNestedFolders(col.folders),
          };
        });

        setCollections(updatedCollections);

        // Mark collection as having unsaved changes
        markCollectionAsUnsaved(collection.id);

        setSnackbar({
          open: true,
          message: 'Folder deleted successfully',
          severity: 'success',
        });
      }
    } else if (itemType === 'request') {
      // Find the collection that contains this request
      const collection = collections.find(col => col.requests[itemId]);

      if (collection) {
        // Delete request from local state
        const updatedCollections = collections.map(col => {
          if (col.id !== collection.id) {
            return col;
          }

          // Remove the request from the requests object
          const { [itemId]: _, ...remainingRequests } = col.requests;

          // Remove the request from any folders
          const updateFolders = (folders: ApiFolder[]): ApiFolder[] => {
            return folders.map(folder => {
              // Remove the request from this folder's requests array
              const updatedRequests = folder.requests.filter(reqId => reqId !== itemId);

              // Recursively update nested folders
              const updatedNestedFolders = updateFolders(folder.folders);

              return {
                ...folder,
                requests: updatedRequests,
                folders: updatedNestedFolders,
              };
            });
          };

          return {
            ...col,
            requests: remainingRequests,
            folders: updateFolders(col.folders),
          };
        });

        setCollections(updatedCollections);

        // Mark collection as having unsaved changes
        markCollectionAsUnsaved(collection.id);

        // If the deleted request was the current request, clear it
        if (currentRequest.id === itemId) {
          setCurrentRequest(createNewRequest());
        }

        setSnackbar({
          open: true,
          message: 'Request deleted successfully',
          severity: 'success',
        });
      }
    }

    handleContextMenuClose();
  };

  // Handle add folder
  const handleAddFolder = (collectionId: string, parentFolderId?: string) => {
    setSelectedCollectionForAction(collectionId);
    setSelectedFolderForAction(parentFolderId || '');
    setCreateFolderDialogOpen(true);
  };

  // Handle add request
  const handleAddRequest = (collectionId: string, folderId?: string) => {
    setSelectedCollectionForAction(collectionId);
    setSelectedFolderForAction(folderId || '');
    setCreateRequestDialogOpen(true);
  };

  // Handle create folder
  const handleCreateFolder = (folderName: string, parentId: string | null, collectionId: string) => {
    const collection = collections.find(c => c.id === collectionId);
    if (!collection) return;

    const newFolder: ApiFolder = {
      id: `folder_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      name: folderName,
      parentId: parentId || undefined,
      requests: [],
      folders: [],
    };

    // Update the collection structure
    const updatedCollection = { ...collection };

    if (parentId) {
      // Add to a specific folder
      const updateFolders = (folders: ApiFolder[]): ApiFolder[] => {
        return folders.map(folder => {
          if (folder.id === parentId) {
            return {
              ...folder,
              folders: [...folder.folders, newFolder],
            };
          }
          if (folder.folders.length > 0) {
            return {
              ...folder,
              folders: updateFolders(folder.folders),
            };
          }
          return folder;
        });
      };

      updatedCollection.folders = updateFolders(updatedCollection.folders);
    } else {
      // Add to root level
      updatedCollection.folders = [...updatedCollection.folders, newFolder];
    }

    // Update collections state
    setCollections(prevCollections =>
      prevCollections.map(c =>
        c.id === collectionId ? updatedCollection : c
      )
    );

    // Mark collection as having unsaved changes
    markCollectionAsUnsaved(collectionId);

    setSnackbar({
      open: true,
      message: 'Folder created successfully',
      severity: 'success',
    });
  };

  // Handle create request
  const handleCreateRequest = (
    requestName: string,
    method: HttpMethod,
    url: string,
    parentId: string | null,
    collectionId: string
  ) => {
    const collection = collections.find(c => c.id === collectionId);
    if (!collection) return;

    const newRequest: ApiRequest = {
      id: `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      name: requestName,
      method,
      url,
      headers: [],
      params: [],
      body: {
        mode: 'none',
        enabled: true,
      },
      preRequestScript: '',
      testScript: '',
    };

    // Update the collection structure
    const updatedCollection = { ...collection };

    // Add the request to the requests object
    updatedCollection.requests = {
      ...updatedCollection.requests,
      [newRequest.id]: newRequest,
    };

    if (parentId) {
      // Add to a specific folder
      const updateFolders = (folders: ApiFolder[]): ApiFolder[] => {
        return folders.map(folder => {
          if (folder.id === parentId) {
            return {
              ...folder,
              requests: [...folder.requests, newRequest.id],
            };
          }
          if (folder.folders.length > 0) {
            return {
              ...folder,
              folders: updateFolders(folder.folders),
            };
          }
          return folder;
        });
      };

      updatedCollection.folders = updateFolders(updatedCollection.folders);
    } else {
      // Add to orphaned requests (root level)
      updatedCollection._orphanedRequests = [
        ...(updatedCollection._orphanedRequests || []),
        newRequest.id,
      ];
    }

    // Update collections state
    setCollections(prevCollections =>
      prevCollections.map(c =>
        c.id === collectionId ? updatedCollection : c
      )
    );

    // Mark collection as having unsaved changes
    markCollectionAsUnsaved(collectionId);

    // Select the new request
    setSelectedItemId(newRequest.id);
    setCurrentRequest(newRequest);

    setSnackbar({
      open: true,
      message: 'Request created successfully',
      severity: 'success',
    });
  };

  // Handle rename actions
  const handleRenameCollection = (collectionId: string, currentName: string) => {
    setRenameDialogData({
      itemType: 'collection',
      itemId: collectionId,
      collectionId,
      currentName,
    });
    setRenameDialogOpen(true);
  };

  const handleRenameFolder = (collectionId: string, folderId: string, currentName: string) => {
    setRenameDialogData({
      itemType: 'folder',
      itemId: folderId,
      collectionId,
      currentName,
    });
    setRenameDialogOpen(true);
  };

  const handleRenameRequest = (collectionId: string, requestId: string, currentName: string) => {
    setRenameDialogData({
      itemType: 'request',
      itemId: requestId,
      collectionId,
      currentName,
    });
    setRenameDialogOpen(true);
  };

  // Handle rename execution
  const handleRename = (newName: string) => {
    if (!renameDialogData) return;

    const { itemType, itemId, collectionId } = renameDialogData;

    if (itemType === 'collection') {
      // Update collection name in database
      postmanConverterApi.updateCollection(collectionId, { name: newName })
        .then(() => {
          // Update collections state
          setCollections(prevCollections =>
            prevCollections.map(c =>
              c.id === collectionId ? { ...c, name: newName } : c
            )
          );

          setSnackbar({
            open: true,
            message: 'Collection renamed successfully',
            severity: 'success',
          });
        })
        .catch(error => {
          errorApi.post(error instanceof Error ? error : new Error(String(error)));
          setSnackbar({
            open: true,
            message: 'Failed to rename collection',
            severity: 'error',
          });
        });
    } else if (itemType === 'folder') {
      const collection = collections.find(c => c.id === collectionId);
      if (!collection) return;

      // Update the folder name in the collection structure
      const updateFolders = (folders: ApiFolder[]): ApiFolder[] => {
        return folders.map(folder => {
          if (folder.id === itemId) {
            return { ...folder, name: newName };
          }
          if (folder.folders.length > 0) {
            return {
              ...folder,
              folders: updateFolders(folder.folders),
            };
          }
          return folder;
        });
      };

      const updatedCollection = {
        ...collection,
        folders: updateFolders(collection.folders),
      };

      // Update collections state
      setCollections(prevCollections =>
        prevCollections.map(c =>
          c.id === collectionId ? updatedCollection : c
        )
      );

      // Mark collection as having unsaved changes
      markCollectionAsUnsaved(collectionId);

      setSnackbar({
        open: true,
        message: 'Folder renamed successfully',
        severity: 'success',
      });
    } else if (itemType === 'request') {
      const collection = collections.find(c => c.id === collectionId);
      if (!collection || !collection.requests[itemId]) return;

      const updatedCollection = {
        ...collection,
        requests: {
          ...collection.requests,
          [itemId]: {
            ...collection.requests[itemId],
            name: newName,
          },
        },
      };

      // Update collections state
      setCollections(prevCollections =>
        prevCollections.map(c =>
          c.id === collectionId ? updatedCollection : c
        )
      );

      // Mark collection as having unsaved changes
      markCollectionAsUnsaved(collectionId);

      // Update current request if it's the one being renamed
      if (currentRequest.id === itemId) {
        setCurrentRequest(prev => ({ ...prev, name: newName }));
      }

      setSnackbar({
        open: true,
        message: 'Request renamed successfully',
        severity: 'success',
      });
    }
  };

  // Handle delete folder
  const handleDeleteFolder = (folderId: string) => {
    // Find the collection that contains this folder
    const collection = collections.find(col => {
      // Check if it's a top-level folder
      if (col.folders.some(folder => folder.id === folderId)) {
        return true;
      }

      // Check if it's a nested folder
      const hasNestedFolder = (folders: ApiFolder[]): boolean => {
        for (const folder of folders) {
          if (folder.id === folderId) {
            return true;
          }

          if (folder.folders.length > 0 && hasNestedFolder(folder.folders)) {
            return true;
          }
        }

        return false;
      };

      return hasNestedFolder(col.folders);
    });

    if (collection) {
      // Delete folder from local state
      const updatedCollections = collections.map(col => {
        if (col.id !== collection.id) {
          return col;
        }

        // Check if the folder is a top-level folder
        const topLevelFolderIndex = col.folders.findIndex(folder => folder.id === folderId);

        if (topLevelFolderIndex !== -1) {
          // It's a top-level folder
          const updatedFolders = [...col.folders];
          updatedFolders.splice(topLevelFolderIndex, 1);

          return {
            ...col,
            folders: updatedFolders,
          };
        }

        // Check if it's a nested folder
        const updateNestedFolders = (folders: ApiFolder[]): ApiFolder[] => {
          return folders.map(folder => {
            // Check if this folder contains the target folder
            const nestedFolderIndex = folder.folders.findIndex(nestedFolder => nestedFolder.id === folderId);

            if (nestedFolderIndex !== -1) {
              // Found the folder to delete
              const updatedNestedFolders = [...folder.folders];
              updatedNestedFolders.splice(nestedFolderIndex, 1);

              return {
                ...folder,
                folders: updatedNestedFolders,
              };
            }

            // Recursively check deeper nested folders
            if (folder.folders.length > 0) {
              return {
                ...folder,
                folders: updateNestedFolders(folder.folders),
              };
            }

            return folder;
          });
        };

        return {
          ...col,
          folders: updateNestedFolders(col.folders),
        };
      });

      setCollections(updatedCollections);

      // Mark collection as having unsaved changes
      markCollectionAsUnsaved(collection.id);

      setSnackbar({
        open: true,
        message: 'Folder deleted successfully',
        severity: 'success',
      });
    }
  };

  return (
    <div className={classes.root}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <InfoCard
            title={
              <Box display="flex" alignItems="center">
                <TestingIcon style={{ marginRight: '8px' }} />
                API Testing
              </Box>
            }
            className={classes.infoCard}
          >
            <Typography variant="body1" paragraph>
              Test your APIs by sending requests and viewing responses. Organize your requests into collections and use environments to manage variables.
            </Typography>
          </InfoCard>
        </Grid>

        {/* Main content grid */}
        <Grid container item xs={12} spacing={3}>
          {/* Collections sidebar */}
          <Grid item xs={12} md={3}>
            <Paper className={classes.paper}>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                <Typography variant="h6">
                  Collections
                </Typography>
                {unsavedCollections.size > 0 && (
                  <Button
                    size="small"
                    color="primary"
                    startIcon={<SaveIcon />}
                    onClick={async () => {
                      // Save all collections with unsaved changes
                      const savePromises = Array.from(unsavedCollections).map(collectionId =>
                        handleSaveCollection(collectionId)
                      );
                      await Promise.all(savePromises);
                    }}
                    disabled={Object.values(isSaving).some(saving => saving)}
                  >
                    Save All ({unsavedCollections.size})
                  </Button>
                )}
              </Box>
              <Divider className={classes.divider} />

              {(() => {
                if (collectionsLoading) {
                  return (
                    <div className={classes.loadingContainer}>
                      <CircularProgress />
                    </div>
                  );
                }

                if (collectionsError) {
                  return <ErrorPanel error={collectionsError} />;
                }

                if (collections.length === 0) {
                  return (
                    <EmptyState
                      missing="data"
                      title="No collections"
                      description="You haven't created any collections yet."
                      action={
                        <Button
                          variant="contained"
                          color="primary"
                          startIcon={<AddIcon />}
                          onClick={handleAddCollection}
                        >
                          Create Collection
                        </Button>
                      }
                    />
                  );
                }

                return (
                  <div className={classes.collectionTree}>
                    <List component="nav" aria-label="collections">
                      {collections.map(collection => {
                        // Check if collection has any content
                        const hasContent = collection.folders.length > 0 || Object.keys(collection.requests).length > 0;
                        const isExpanded = expandedFolders[`collection_${collection.id}`] || false;
                        const hasUnsavedChanges = unsavedCollections.has(collection.id);
                        const isSavingCollection = isSaving[collection.id] || false;

                        return (
                          <React.Fragment key={collection.id}>
                            <ListItem
                              button
                              selected={selectedItemId === collection.id}
                              onClick={() => handleItemSelect(collection.id)}
                              onContextMenu={(e) => handleContextMenu(e, collection.id, 'collection')}
                            >
                              <ListItemIcon onClick={(e) => {
                                e.stopPropagation();
                                handleFolderToggle(`collection_${collection.id}`);
                              }}>
                                {isExpanded ? <ExpandMoreIcon /> : <ChevronRightIcon />}
                              </ListItemIcon>
                              <ListItemIcon>
                                {hasUnsavedChanges ? (
                                  <Badge color="secondary" variant="dot">
                                    <FolderIcon color="primary" />
                                  </Badge>
                                ) : (
                                  <FolderIcon color="primary" />
                                )}
                              </ListItemIcon>
                              <ListItemText
                                primary={
                                  <Box display="flex" alignItems="center">
                                    <span>{collection.name}</span>
                                    {hasUnsavedChanges && (
                                      <Chip
                                        label="Unsaved"
                                        size="small"
                                        color="secondary"
                                        style={{ marginLeft: '8px', fontSize: '0.7rem' }}
                                      />
                                    )}
                                  </Box>
                                }
                              />
                              {hasUnsavedChanges && (
                                <Tooltip title="Save collection changes">
                                  <IconButton
                                    size="small"
                                    color="primary"
                                    disabled={isSavingCollection}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleSaveCollection(collection.id);
                                    }}
                                    style={{ marginRight: '4px' }}
                                  >
                                    {isSavingCollection ? (
                                      <CircularProgress size={16} />
                                    ) : (
                                      <SaveIcon />
                                    )}
                                  </IconButton>
                                </Tooltip>
                              )}
                              <IconButton
                                edge="end"
                                size="small"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleContextMenu(e, collection.id, 'collection');
                                }}
                              >
                                <MoreVertIcon />
                              </IconButton>
                            </ListItem>

                            {/* Render folders and requests */}
                            <Collapse in={isExpanded} timeout="auto" unmountOnExit>
                              <List component="div" disablePadding>
                                {/* Render orphaned requests at the root level */}
                                {collection._orphanedRequests && collection._orphanedRequests.length > 0 && (
                                  <div>
                                    <ListItem style={{ paddingLeft: '16px', backgroundColor: '#f5f5f5' }}>
                                      <ListItemText
                                        primary="Root Requests"
                                        secondary={`${collection._orphanedRequests.length} requests not in folders`}
                                      />
                                    </ListItem>
                                    {collection._orphanedRequests.map(requestId => {
                                      const request = collection.requests[requestId];
                                      if (!request) return null;

                                      return (
                                        <ListItem
                                          key={requestId}
                                          button
                                          style={{ paddingLeft: '32px' }}
                                          selected={selectedItemId === requestId}
                                          onClick={() => handleItemSelect(requestId)}
                                          onContextMenu={(e) => handleContextMenu(e, requestId, 'request')}
                                        >
                                          <ListItemIcon>
                                            <DescriptionIcon />
                                          </ListItemIcon>
                                          <ListItemText
                                            primary={
                                              <Box display="flex" alignItems="center">
                                                <Chip
                                                  label={request.method}
                                                  size="small"
                                                  style={{
                                                    backgroundColor: getMethodColor(request.method),
                                                    color: 'white',
                                                    marginRight: '8px',
                                                    fontWeight: 'bold',
                                                    minWidth: '60px',
                                                    textAlign: 'center'
                                                  }}
                                                />
                                                <span>{request.name}</span>
                                              </Box>
                                            }
                                            secondary={request.url}
                                            primaryTypographyProps={{
                                              style: {
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                whiteSpace: 'nowrap',
                                              }
                                            }}
                                            secondaryTypographyProps={{
                                              style: {
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                whiteSpace: 'nowrap',
                                              }
                                            }}
                                          />
                                          <IconButton
                                            edge="end"
                                            size="small"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              handleContextMenu(e, requestId, 'request');
                                            }}
                                          >
                                            <MoreVertIcon />
                                          </IconButton>
                                        </ListItem>
                                      );
                                    })}
                                  </div>
                                )}

                                {/* Render folders */}
                                {collection.folders.map(folder => (
                                  <RenderFolder
                                    key={folder.id}
                                    folder={folder}
                                    collection={collection}
                                    level={1}
                                    expandedFolders={expandedFolders}
                                    selectedItemId={selectedItemId}
                                    onFolderToggle={handleFolderToggle}
                                    onItemSelect={handleItemSelect}
                                    onContextMenu={handleContextMenu}
                                  />
                                ))}

                                {/* Show message if collection is empty */}
                                {!hasContent && !collection._orphanedRequests?.length && (
                                  <ListItem style={{ paddingLeft: '16px' }}>
                                    <ListItemText
                                      primary="Empty collection"
                                      secondary="This collection has no requests or folders"
                                      primaryTypographyProps={{ style: { color: '#999' } }}
                                      secondaryTypographyProps={{ style: { color: '#bbb' } }}
                                    />
                                  </ListItem>
                                )}
                              </List>
                            </Collapse>
                          </React.Fragment>
                        );
                      })}
                    </List>


                    {/* Context Menu */}
                    <Menu
                      keepMounted
                      open={contextMenu !== null}
                      onClose={handleContextMenuClose}
                      anchorReference="anchorPosition"
                      anchorPosition={
                        contextMenu !== null
                          ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
                          : undefined
                      }
                    >
                      {contextMenu && (
                        <>
                          {contextMenu.itemType === 'collection' && (
                            <>
                              <MenuItem onClick={() => {
                                handleAddFolder(contextMenu.itemId);
                                handleContextMenuClose();
                              }}>
                                <ListItemIcon>
                                  <FolderIcon fontSize="small" />
                                </ListItemIcon>
                                <ListItemText primary="Add Folder" />
                              </MenuItem>
                              <MenuItem onClick={() => {
                                handleAddRequest(contextMenu.itemId);
                                handleContextMenuClose();
                              }}>
                                <ListItemIcon>
                                  <AddIcon fontSize="small" />
                                </ListItemIcon>
                                <ListItemText primary="Add Request" />
                              </MenuItem>
                              <MenuItem onClick={() => {
                                const collection = collections.find(c => c.id === contextMenu.itemId);
                                if (collection) {
                                  handleRenameCollection(contextMenu.itemId, collection.name);
                                }
                                handleContextMenuClose();
                              }}>
                                <ListItemIcon>
                                  <EditIcon fontSize="small" />
                                </ListItemIcon>
                                <ListItemText primary="Rename" />
                              </MenuItem>
                              <MenuItem onClick={() => {
                                handleEditCollection(contextMenu.itemId);
                                handleContextMenuClose();
                              }}>
                                <ListItemIcon>
                                  <EditIcon fontSize="small" />
                                </ListItemIcon>
                                <ListItemText primary="Edit" />
                              </MenuItem>
                              <MenuItem onClick={() => {
                                handleDeleteItem(contextMenu.itemId, 'collection');
                                handleContextMenuClose();
                              }}>
                                <ListItemIcon>
                                  <DeleteIcon fontSize="small" />
                                </ListItemIcon>
                                <ListItemText primary="Delete" />
                              </MenuItem>
                            </>
                          )}

                          {contextMenu.itemType === 'folder' && (
                            <>
                              <MenuItem onClick={() => {
                                // Find the collection that contains this folder
                                const collection = collections.find(col => {
                                  const findFolder = (folders: ApiFolder[]): boolean => {
                                    return folders.some(f =>
                                      f.id === contextMenu.itemId ||
                                      (f.folders.length > 0 && findFolder(f.folders))
                                    );
                                  };
                                  return findFolder(col.folders);
                                });

                                if (collection) {
                                  handleAddFolder(collection.id, contextMenu.itemId);
                                }
                                handleContextMenuClose();
                              }}>
                                <ListItemIcon>
                                  <FolderIcon fontSize="small" />
                                </ListItemIcon>
                                <ListItemText primary="Add Folder" />
                              </MenuItem>
                              <MenuItem onClick={() => {
                                // Find the collection that contains this folder
                                const collection = collections.find(col => {
                                  const findFolder = (folders: ApiFolder[]): boolean => {
                                    return folders.some(f =>
                                      f.id === contextMenu.itemId ||
                                      (f.folders.length > 0 && findFolder(f.folders))
                                    );
                                  };
                                  return findFolder(col.folders);
                                });

                                if (collection) {
                                  handleAddRequest(collection.id, contextMenu.itemId);
                                }
                                handleContextMenuClose();
                              }}>
                                <ListItemIcon>
                                  <AddIcon fontSize="small" />
                                </ListItemIcon>
                                <ListItemText primary="Add Request" />
                              </MenuItem>
                              <MenuItem onClick={() => {
                                // Find the collection and folder to get the current name
                                const collection = collections.find(col => {
                                  const findFolder = (folders: ApiFolder[]): boolean => {
                                    return folders.some(f =>
                                      f.id === contextMenu.itemId ||
                                      (f.folders.length > 0 && findFolder(f.folders))
                                    );
                                  };
                                  return findFolder(col.folders);
                                });

                                if (collection) {
                                  // Find the folder to get its current name
                                  const findFolderName = (folders: ApiFolder[]): string | null => {
                                    for (const folder of folders) {
                                      if (folder.id === contextMenu.itemId) {
                                        return folder.name;
                                      }
                                      if (folder.folders.length > 0) {
                                        const nestedName = findFolderName(folder.folders);
                                        if (nestedName) return nestedName;
                                      }
                                    }
                                    return null;
                                  };

                                  const folderName = findFolderName(collection.folders);
                                  if (folderName) {
                                    handleRenameFolder(collection.id, contextMenu.itemId, folderName);
                                  }
                                }
                                handleContextMenuClose();
                              }}>
                                <ListItemIcon>
                                  <EditIcon fontSize="small" />
                                </ListItemIcon>
                                <ListItemText primary="Rename" />
                              </MenuItem>
                              <MenuItem onClick={() => {
                                handleDeleteFolder(contextMenu.itemId);
                                handleContextMenuClose();
                              }}>
                                <ListItemIcon>
                                  <DeleteIcon fontSize="small" />
                                </ListItemIcon>
                                <ListItemText primary="Delete" />
                              </MenuItem>
                            </>
                          )}

                          {contextMenu.itemType === 'request' && (
                            <>
                              <MenuItem onClick={() => {
                                // Find the collection and request to get the current name
                                const collection = collections.find(c => c.requests[contextMenu.itemId]);
                                if (collection && collection.requests[contextMenu.itemId]) {
                                  const request = collection.requests[contextMenu.itemId];
                                  handleRenameRequest(collection.id, contextMenu.itemId, request.name);
                                }
                                handleContextMenuClose();
                              }}>
                                <ListItemIcon>
                                  <EditIcon fontSize="small" />
                                </ListItemIcon>
                                <ListItemText primary="Rename" />
                              </MenuItem>
                              <MenuItem onClick={() => {
                                handleDuplicateRequest(contextMenu.itemId);
                                handleContextMenuClose();
                              }}>
                                <ListItemIcon>
                                  <FileCopyIcon fontSize="small" />
                                </ListItemIcon>
                                <ListItemText primary="Duplicate" />
                              </MenuItem>
                              <MenuItem onClick={() => {
                                handleDeleteItem(contextMenu.itemId, 'request');
                                handleContextMenuClose();
                              }}>
                                <ListItemIcon>
                                  <DeleteIcon fontSize="small" />
                                </ListItemIcon>
                                <ListItemText primary="Delete" />
                              </MenuItem>
                            </>
                          )}
                        </>
                      )}
                    </Menu>
                  </div>
                );
              })()}
            </Paper>
          </Grid>

          {/* Request and response area */}
          <Grid item xs={12} md={9}>
            <Paper className={classes.paper}>
              {/* Environment selector */}
              <div className={classes.environmentSelector}>
                <FormControl className={classes.envSelect}>
                  <InputLabel id="environment-select-label">Environment</InputLabel>
                  <Select
                    labelId="environment-select-label"
                    id="environment-select"
                    value={currentEnvironment}
                    onChange={handleEnvironmentChange}
                  >
                    {environments.map(env => (
                      <MenuItem key={env.id} value={env.id}>
                        {env.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                <div className={classes.envActions}>
                  <Tooltip title="Manage Environments">
                    <IconButton onClick={() => {
                      // Open environment management dialog
                      setEnvironmentDialogOpen(true);
                    }}>
                      <SettingsIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Import/Export">
                    <IconButton onClick={handleOpenExportDialog}>
                      <ImportExportIcon />
                    </IconButton>
                  </Tooltip>
                </div>
              </div>

              {/* Environment Variables Dialog */}
              <Dialog
                open={environmentDialogOpen}
                onClose={() => setEnvironmentDialogOpen(false)}
                fullWidth
                maxWidth="md"
              >
                <DialogTitle>
                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Typography variant="h6">Manage Environments</Typography>
                    <IconButton onClick={() => setEnvironmentDialogOpen(false)}>
                      <CloseIcon />
                    </IconButton>
                  </Box>
                </DialogTitle>
                <DialogContent dividers>
                  <Box mb={2}>
                    <Typography variant="subtitle1" gutterBottom>
                      Environments
                    </Typography>
                    <Paper variant="outlined">
                      <List>
                        {environments.map((env, index) => (
                          <ListItem
                            key={env.id}
                            button
                            selected={env.id === selectedEnvironmentId}
                            onClick={() => setSelectedEnvironmentId(env.id)}
                          >
                            <ListItemText
                              primary={env.name}
                              secondary={`${env.variables.length} variables`}
                            />
                            <ListItemSecondaryAction>
                              <IconButton
                                edge="end"
                                onClick={() => {
                                  // Handle environment deletion
                                  const newEnvironments = [...environments];
                                  newEnvironments.splice(index, 1);
                                  if (newEnvironments.length === 0) {
                                    newEnvironments.push(createNewEnvironment());
                                  }
                                  setEnvironments(newEnvironments);
                                  if (env.id === selectedEnvironmentId) {
                                    setSelectedEnvironmentId(newEnvironments[0].id);
                                  }
                                }}
                              >
                                <DeleteIcon />
                              </IconButton>
                            </ListItemSecondaryAction>
                          </ListItem>
                        ))}
                      </List>
                    </Paper>
                    <Button
                      startIcon={<AddIcon />}
                      color="primary"
                      className={classes.addButton}
                      onClick={() => {
                        // Add new environment
                        const newEnv = createNewEnvironment();
                        setEnvironments([...environments, newEnv]);
                        setSelectedEnvironmentId(newEnv.id);
                      }}
                    >
                      Add Environment
                    </Button>
                  </Box>

                  {selectedEnvironment && (
                    <Box>
                      <Box mb={2}>
                        <TextField
                          label="Environment Name"
                          variant="outlined"
                          fullWidth
                          value={selectedEnvironment.name}
                          onChange={(e) => {
                            const newEnvironments = [...environments];
                            const envIndex = newEnvironments.findIndex(env => env.id === selectedEnvironmentId);
                            if (envIndex !== -1) {
                              newEnvironments[envIndex] = {
                                ...newEnvironments[envIndex],
                                name: e.target.value,
                              };
                              setEnvironments(newEnvironments);
                            }
                          }}
                        />
                      </Box>

                      <Typography variant="subtitle1" gutterBottom>
                        Variables
                      </Typography>
                      <Paper variant="outlined">
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell>Variable</TableCell>
                              <TableCell>Value</TableCell>
                              <TableCell width="100">Enabled</TableCell>
                              <TableCell width="70">Actions</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {selectedEnvironment.variables.map((variable, index) => (
                              <TableRow key={index}>
                                <TableCell>
                                  <TextField
                                    size="small"
                                    fullWidth
                                    value={variable.key}
                                    onChange={(e) => {
                                      const newEnvironments = [...environments];
                                      const envIndex = newEnvironments.findIndex(env => env.id === selectedEnvironmentId);
                                      if (envIndex !== -1) {
                                        const newVariables = [...newEnvironments[envIndex].variables];
                                        newVariables[index] = {
                                          ...newVariables[index],
                                          key: e.target.value,
                                        };
                                        newEnvironments[envIndex] = {
                                          ...newEnvironments[envIndex],
                                          variables: newVariables,
                                        };
                                        setEnvironments(newEnvironments);
                                      }
                                    }}
                                  />
                                </TableCell>
                                <TableCell>
                                  <TextField
                                    size="small"
                                    fullWidth
                                    value={variable.value}
                                    onChange={(e) => {
                                      const newEnvironments = [...environments];
                                      const envIndex = newEnvironments.findIndex(env => env.id === selectedEnvironmentId);
                                      if (envIndex !== -1) {
                                        const newVariables = [...newEnvironments[envIndex].variables];
                                        newVariables[index] = {
                                          ...newVariables[index],
                                          value: e.target.value,
                                        };
                                        newEnvironments[envIndex] = {
                                          ...newEnvironments[envIndex],
                                          variables: newVariables,
                                        };
                                        setEnvironments(newEnvironments);
                                      }
                                    }}
                                  />
                                </TableCell>
                                <TableCell align="center">
                                  <Checkbox
                                    checked={variable.enabled}
                                    onChange={(e) => {
                                      const newEnvironments = [...environments];
                                      const envIndex = newEnvironments.findIndex(env => env.id === selectedEnvironmentId);
                                      if (envIndex !== -1) {
                                        const newVariables = [...newEnvironments[envIndex].variables];
                                        newVariables[index] = {
                                          ...newVariables[index],
                                          enabled: e.target.checked,
                                        };
                                        newEnvironments[envIndex] = {
                                          ...newEnvironments[envIndex],
                                          variables: newVariables,
                                        };
                                        setEnvironments(newEnvironments);
                                      }
                                    }}
                                  />
                                </TableCell>
                                <TableCell>
                                  <IconButton
                                    size="small"
                                    onClick={() => {
                                      const newEnvironments = [...environments];
                                      const envIndex = newEnvironments.findIndex(env => env.id === selectedEnvironmentId);
                                      if (envIndex !== -1) {
                                        const newVariables = [...newEnvironments[envIndex].variables];
                                        newVariables.splice(index, 1);
                                        newEnvironments[envIndex] = {
                                          ...newEnvironments[envIndex],
                                          variables: newVariables,
                                        };
                                        setEnvironments(newEnvironments);
                                      }
                                    }}
                                  >
                                    <DeleteIcon />
                                  </IconButton>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </Paper>
                      <Button
                        startIcon={<AddIcon />}
                        color="primary"
                        className={classes.addButton}
                        onClick={() => {
                          const newEnvironments = [...environments];
                          const envIndex = newEnvironments.findIndex(env => env.id === selectedEnvironmentId);
                          if (envIndex !== -1) {
                            const newVariables = [...newEnvironments[envIndex].variables];
                            newVariables.push({ key: '', value: '', enabled: true });
                            newEnvironments[envIndex] = {
                              ...newEnvironments[envIndex],
                              variables: newVariables,
                            };
                            setEnvironments(newEnvironments);
                          }
                        }}
                      >
                        Add Variable
                      </Button>
                    </Box>
                  )}
                </DialogContent>
                <DialogActions>
                  <Button onClick={() => setEnvironmentDialogOpen(false)} color="primary">
                    Close
                  </Button>
                </DialogActions>
              </Dialog>

              {/* Request URL bar */}
              <div className={classes.urlBar}>
                <FormControl className={classes.methodSelect}>
                  <Select
                    value={currentRequest.method}
                    onChange={handleMethodChange}
                    variant="outlined"
                  >
                    <MenuItem value="GET">GET</MenuItem>
                    <MenuItem value="POST">POST</MenuItem>
                    <MenuItem value="PUT">PUT</MenuItem>
                    <MenuItem value="DELETE">DELETE</MenuItem>
                    <MenuItem value="PATCH">PATCH</MenuItem>
                    <MenuItem value="HEAD">HEAD</MenuItem>
                    <MenuItem value="OPTIONS">OPTIONS</MenuItem>
                  </Select>
                </FormControl>
                <TextField
                  className={classes.urlField}
                  variant="outlined"
                  size="small"
                  placeholder="Enter request URL"
                  value={currentRequest.url}
                  onChange={handleUrlChange}
                  helperText={getUrlHelperText(currentRequest.url)}
                />
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
                  onClick={handleSendRequest}
                  disabled={isLoading || !currentRequest.url}
                >
                  Send
                </Button>
              </div>

              {/* Request tabs */}
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                indicatorColor="primary"
                textColor="primary"
              >
                <Tab label="Params" />
                <Tab label="Headers" />
                <Tab label="Body" />
                <Tab label="Auth" />
                <Tab label="Pre-request" />
                <Tab label="Tests" />
              </Tabs>

              {/* Params tab */}
              <TabPanel value={tabValue} index={0}>
                <Typography variant="subtitle2" gutterBottom>
                  Query Parameters
                </Typography>
                <div>
                  {currentRequest.params.map((param, index) => (
                    <div key={index} className={classes.keyValueRow}>
                      <TextField
                        className={classes.keyValueKey}
                        size="small"
                        label="Key"
                        variant="outlined"
                        value={param.key}
                        onChange={(e) => {
                          const newParams = [...currentRequest.params];
                          newParams[index].key = e.target.value;
                          setCurrentRequest({
                            ...currentRequest,
                            params: newParams,
                          });
                        }}
                      />
                      <TextField
                        className={classes.keyValueValue}
                        size="small"
                        label="Value"
                        variant="outlined"
                        value={param.value}
                        onChange={(e) => {
                          const newParams = [...currentRequest.params];
                          newParams[index].value = e.target.value;
                          setCurrentRequest({
                            ...currentRequest,
                            params: newParams,
                          });
                        }}
                      />
                      <div className={classes.keyValueActions}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={param.enabled}
                              onChange={(e) => {
                                const newParams = [...currentRequest.params];
                                newParams[index].enabled = e.target.checked;
                                setCurrentRequest({
                                  ...currentRequest,
                                  params: newParams,
                                });
                              }}
                              size="small"
                            />
                          }
                          label="Enabled"
                        />
                        <IconButton
                          size="small"
                          onClick={() => {
                            const newParams = [...currentRequest.params];
                            newParams.splice(index, 1);
                            setCurrentRequest({
                              ...currentRequest,
                              params: newParams,
                            });
                          }}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </div>
                    </div>
                  ))}
                  <Button
                    startIcon={<AddIcon />}
                    color="primary"
                    className={classes.addButton}
                    onClick={() => {
                      setCurrentRequest({
                        ...currentRequest,
                        params: [
                          ...currentRequest.params,
                          { key: '', value: '', enabled: true },
                        ],
                      });
                    }}
                  >
                    Add Parameter
                  </Button>
                </div>
              </TabPanel>

              {/* Headers tab */}
              <TabPanel value={tabValue} index={1}>
                <Typography variant="subtitle2" gutterBottom>
                  Headers
                </Typography>
                <div>
                  {currentRequest.headers.map((header, index) => (
                    <div key={index} className={classes.keyValueRow}>
                      <TextField
                        className={classes.keyValueKey}
                        size="small"
                        label="Key"
                        variant="outlined"
                        value={header.key}
                        onChange={(e) => {
                          const newHeaders = [...currentRequest.headers];
                          newHeaders[index].key = e.target.value;
                          setCurrentRequest({
                            ...currentRequest,
                            headers: newHeaders,
                          });
                        }}
                      />
                      <TextField
                        className={classes.keyValueValue}
                        size="small"
                        label="Value"
                        variant="outlined"
                        value={header.value}
                        onChange={(e) => {
                          const newHeaders = [...currentRequest.headers];
                          newHeaders[index].value = e.target.value;
                          setCurrentRequest({
                            ...currentRequest,
                            headers: newHeaders,
                          });
                        }}
                      />
                      <div className={classes.keyValueActions}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={header.enabled}
                              onChange={(e) => {
                                const newHeaders = [...currentRequest.headers];
                                newHeaders[index].enabled = e.target.checked;
                                setCurrentRequest({
                                  ...currentRequest,
                                  headers: newHeaders,
                                });
                              }}
                              size="small"
                            />
                          }
                          label="Enabled"
                        />
                        <IconButton
                          size="small"
                          onClick={() => {
                            const newHeaders = [...currentRequest.headers];
                            newHeaders.splice(index, 1);
                            setCurrentRequest({
                              ...currentRequest,
                              headers: newHeaders,
                            });
                          }}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </div>
                    </div>
                  ))}
                  <Button
                    startIcon={<AddIcon />}
                    color="primary"
                    className={classes.addButton}
                    onClick={() => {
                      setCurrentRequest({
                        ...currentRequest,
                        headers: [
                          ...currentRequest.headers,
                          { key: '', value: '', enabled: true },
                        ],
                      });
                    }}
                  >
                    Add Header
                  </Button>
                </div>
              </TabPanel>

              {/* Body tab */}
              <TabPanel value={tabValue} index={2}>
                <Typography variant="subtitle2" gutterBottom>
                  Request Body
                </Typography>
                <div>
                  <FormControl className={classes.formControl} fullWidth>
                    <InputLabel id="body-mode-select-label">Body Type</InputLabel>
                    <Select
                      labelId="body-mode-select-label"
                      id="body-mode-select"
                      value={currentRequest.body.mode}
                      onChange={(e) => {
                        setCurrentRequest({
                          ...currentRequest,
                          body: {
                            ...currentRequest.body,
                            mode: e.target.value as 'none' | 'raw' | 'form-data' | 'urlencoded',
                          },
                        });
                      }}
                    >
                      <MenuItem value="none">None</MenuItem>
                      <MenuItem value="raw">Raw</MenuItem>
                      <MenuItem value="form-data">Form Data</MenuItem>
                      <MenuItem value="urlencoded">URL Encoded</MenuItem>
                    </Select>
                  </FormControl>

                  {currentRequest.body.mode === 'raw' && (
                    <div style={{ marginTop: '16px' }}>
                      <TextField
                        label="Raw Body"
                        multiline
                        minRows={8}
                        variant="outlined"
                        fullWidth
                        className={classes.bodyEditor}
                        value={currentRequest.body.raw || ''}
                        onChange={(e) => {
                          setCurrentRequest({
                            ...currentRequest,
                            body: {
                              ...currentRequest.body,
                              raw: e.target.value,
                            },
                          });
                        }}
                      />
                    </div>
                  )}

                  {currentRequest.body.mode === 'form-data' && (
                    <div style={{ marginTop: '16px' }}>
                      {(currentRequest.body.formData || []).map((item, index) => (
                        <div key={index} className={classes.keyValueRow}>
                          <TextField
                            className={classes.keyValueKey}
                            size="small"
                            label="Key"
                            variant="outlined"
                            value={item.key}
                            onChange={(e) => {
                              const newFormData = [...(currentRequest.body.formData || [])];
                              newFormData[index].key = e.target.value;
                              setCurrentRequest({
                                ...currentRequest,
                                body: {
                                  ...currentRequest.body,
                                  formData: newFormData,
                                },
                              });
                            }}
                          />
                          <TextField
                            className={classes.keyValueValue}
                            size="small"
                            label="Value"
                            variant="outlined"
                            value={item.value}
                            onChange={(e) => {
                              const newFormData = [...(currentRequest.body.formData || [])];
                              newFormData[index].value = e.target.value;
                              setCurrentRequest({
                                ...currentRequest,
                                body: {
                                  ...currentRequest.body,
                                  formData: newFormData,
                                },
                              });
                            }}
                          />
                          <FormControl className={classes.formControl} style={{ minWidth: '100px' }}>
                            <InputLabel id={`form-data-type-${index}-label`}>Type</InputLabel>
                            <Select
                              labelId={`form-data-type-${index}-label`}
                              id={`form-data-type-${index}`}
                              value={item.type}
                              onChange={(e) => {
                                const newFormData = [...(currentRequest.body.formData || [])];
                                newFormData[index].type = e.target.value as string;
                                setCurrentRequest({
                                  ...currentRequest,
                                  body: {
                                    ...currentRequest.body,
                                    formData: newFormData,
                                  },
                                });
                              }}
                            >
                              <MenuItem value="text">Text</MenuItem>
                              <MenuItem value="file">File</MenuItem>
                            </Select>
                          </FormControl>
                          <IconButton
                            size="small"
                            onClick={() => {
                              const newFormData = [...(currentRequest.body.formData || [])];
                              newFormData.splice(index, 1);
                              setCurrentRequest({
                                ...currentRequest,
                                body: {
                                  ...currentRequest.body,
                                  formData: newFormData,
                                },
                              });
                            }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </div>
                      ))}
                      <Button
                        startIcon={<AddIcon />}
                        color="primary"
                        className={classes.addButton}
                        onClick={() => {
                          setCurrentRequest({
                            ...currentRequest,
                            body: {
                              ...currentRequest.body,
                              formData: [
                                ...(currentRequest.body.formData || []),
                                { key: '', value: '', type: 'text' },
                              ],
                            },
                          });
                        }}
                      >
                        Add Form Data
                      </Button>
                    </div>
                  )}

                  {currentRequest.body.mode === 'urlencoded' && (
                    <div style={{ marginTop: '16px' }}>
                      {(currentRequest.body.urlencoded || []).map((item, index) => (
                        <div key={index} className={classes.keyValueRow}>
                          <TextField
                            className={classes.keyValueKey}
                            size="small"
                            label="Key"
                            variant="outlined"
                            value={item.key}
                            onChange={(e) => {
                              const newUrlencoded = [...(currentRequest.body.urlencoded || [])];
                              newUrlencoded[index].key = e.target.value;
                              setCurrentRequest({
                                ...currentRequest,
                                body: {
                                  ...currentRequest.body,
                                  urlencoded: newUrlencoded,
                                },
                              });
                            }}
                          />
                          <TextField
                            className={classes.keyValueValue}
                            size="small"
                            label="Value"
                            variant="outlined"
                            value={item.value}
                            onChange={(e) => {
                              const newUrlencoded = [...(currentRequest.body.urlencoded || [])];
                              newUrlencoded[index].value = e.target.value;
                              setCurrentRequest({
                                ...currentRequest,
                                body: {
                                  ...currentRequest.body,
                                  urlencoded: newUrlencoded,
                                },
                              });
                            }}
                          />
                          <IconButton
                            size="small"
                            onClick={() => {
                              const newUrlencoded = [...(currentRequest.body.urlencoded || [])];
                              newUrlencoded.splice(index, 1);
                              setCurrentRequest({
                                ...currentRequest,
                                body: {
                                  ...currentRequest.body,
                                  urlencoded: newUrlencoded,
                                },
                              });
                            }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </div>
                      ))}
                      <Button
                        startIcon={<AddIcon />}
                        color="primary"
                        className={classes.addButton}
                        onClick={() => {
                          setCurrentRequest({
                            ...currentRequest,
                            body: {
                              ...currentRequest.body,
                              urlencoded: [
                                ...(currentRequest.body.urlencoded || []),
                                { key: '', value: '' },
                              ],
                            },
                          });
                        }}
                      >
                        Add URL Encoded
                      </Button>
                    </div>
                  )}
                </div>
              </TabPanel>

              {/* Auth tab */}
              <TabPanel value={tabValue} index={3}>
                <Typography variant="subtitle2" gutterBottom>
                  Authentication
                </Typography>
                <div>
                  <FormControl className={classes.formControl} fullWidth>
                    <InputLabel id="auth-type-select-label">Auth Type</InputLabel>
                    <Select
                      labelId="auth-type-select-label"
                      id="auth-type-select"
                      value={currentRequest.auth?.type || 'none'}
                      onChange={(e) => {
                        const authType = e.target.value as 'none' | 'basic' | 'bearer' | 'apiKey';
                        let auth: ApiRequest['auth'];

                        switch (authType) {
                          case 'basic':
                            auth = {
                              type: 'basic',
                              basic: { username: '', password: '' },
                            };
                            break;
                          case 'bearer':
                            auth = {
                              type: 'bearer',
                              bearer: { token: '' },
                            };
                            break;
                          case 'apiKey':
                            auth = {
                              type: 'apiKey',
                              apiKey: { key: '', value: '', in: 'header' },
                            };
                            break;
                          default:
                            auth = { type: 'none' };
                        }

                        setCurrentRequest({
                          ...currentRequest,
                          auth,
                        });
                      }}
                    >
                      <MenuItem value="none">No Auth</MenuItem>
                      <MenuItem value="basic">Basic Auth</MenuItem>
                      <MenuItem value="bearer">Bearer Token</MenuItem>
                      <MenuItem value="apiKey">API Key</MenuItem>
                    </Select>
                  </FormControl>

                  {currentRequest.auth?.type === 'basic' && (
                    <div style={{ marginTop: '16px' }}>
                      <TextField
                        label="Username"
                        variant="outlined"
                        fullWidth
                        margin="normal"
                        value={currentRequest.auth.basic?.username || ''}
                        onChange={(e) => {
                          if (currentRequest.auth?.type === 'basic') {
                            setCurrentRequest({
                              ...currentRequest,
                              auth: {
                                type: 'basic',
                                basic: {
                                  ...currentRequest.auth.basic!,
                                  username: e.target.value,
                                },
                              },
                            });
                          }
                        }}
                      />
                      <TextField
                        label="Password"
                        type="password"
                        variant="outlined"
                        fullWidth
                        margin="normal"
                        value={currentRequest.auth.basic?.password || ''}
                        onChange={(e) => {
                          if (currentRequest.auth?.type === 'basic') {
                            setCurrentRequest({
                              ...currentRequest,
                              auth: {
                                type: 'basic',
                                basic: {
                                  ...currentRequest.auth.basic!,
                                  password: e.target.value,
                                },
                              },
                            });
                          }
                        }}
                      />
                    </div>
                  )}

                  {currentRequest.auth?.type === 'bearer' && (
                    <div style={{ marginTop: '16px' }}>
                      <TextField
                        label="Token"
                        variant="outlined"
                        fullWidth
                        margin="normal"
                        value={currentRequest.auth.bearer?.token || ''}
                        onChange={(e) => {
                          if (currentRequest.auth?.type === 'bearer') {
                            setCurrentRequest({
                              ...currentRequest,
                              auth: {
                                type: 'bearer',
                                bearer: {
                                  token: e.target.value,
                                },
                              },
                            });
                          }
                        }}
                      />
                    </div>
                  )}

                  {currentRequest.auth?.type === 'apiKey' && (
                    <div style={{ marginTop: '16px' }}>
                      <TextField
                        label="Key Name"
                        variant="outlined"
                        fullWidth
                        margin="normal"
                        value={currentRequest.auth.apiKey?.key || ''}
                        onChange={(e) => {
                          if (currentRequest.auth?.type === 'apiKey' && currentRequest.auth.apiKey) {
                            setCurrentRequest({
                              ...currentRequest,
                              auth: {
                                type: 'apiKey',
                                apiKey: {
                                  ...currentRequest.auth.apiKey,
                                  key: e.target.value,
                                },
                              },
                            });
                          }
                        }}
                      />
                      <TextField
                        label="Value"
                        variant="outlined"
                        fullWidth
                        margin="normal"
                        value={currentRequest.auth.apiKey?.value || ''}
                        onChange={(e) => {
                          if (currentRequest.auth?.type === 'apiKey' && currentRequest.auth.apiKey) {
                            setCurrentRequest({
                              ...currentRequest,
                              auth: {
                                type: 'apiKey',
                                apiKey: {
                                  ...currentRequest.auth.apiKey,
                                  value: e.target.value,
                                },
                              },
                            });
                          }
                        }}
                      />
                      <FormControl fullWidth margin="normal">
                        <InputLabel id="api-key-location-label">Add to</InputLabel>
                        <Select
                          labelId="api-key-location-label"
                          id="api-key-location"
                          value={currentRequest.auth.apiKey?.in || 'header'}
                          onChange={(e) => {
                            if (currentRequest.auth?.type === 'apiKey' && currentRequest.auth.apiKey) {
                              setCurrentRequest({
                                ...currentRequest,
                                auth: {
                                  type: 'apiKey',
                                  apiKey: {
                                    ...currentRequest.auth.apiKey,
                                    in: e.target.value as 'header' | 'query',
                                  },
                                },
                              });
                            }
                          }}
                        >
                          <MenuItem value="header">Header</MenuItem>
                          <MenuItem value="query">Query Parameter</MenuItem>
                        </Select>
                      </FormControl>
                    </div>
                  )}
                </div>
              </TabPanel>

              {/* Pre-request script tab */}
              <TabPanel value={tabValue} index={4}>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <PreRequestScriptPanel
                      request={currentRequest}
                      onSaveScript={handleSavePreRequestScript}
                      isSaving={isSavingPreRequestScript}
                      error={preRequestScriptError}
                    />
                  </Grid>
                </Grid>
              </TabPanel>

              {/* Tests tab */}
              <TabPanel value={tabValue} index={5}>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TestGeneratorPanel
                      request={currentRequest}
                      response={currentResponse}
                      onRunTests={handleRunTests}
                      onSaveTests={handleSaveTests}
                      isGenerating={isGeneratingTests}
                      isRunning={isRunningTests}
                      error={testError}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TestResultsPanel
                      results={testResults}
                      isRunning={isRunningTests}
                      error={testError}
                    />
                  </Grid>

                  {!currentResponse && (
                    <Grid item xs={12}>
                      <Alert severity="info">
                        Send a request to generate tests based on the response.
                      </Alert>
                    </Grid>
                  )}

                  {currentResponse && !currentRequest.testScript && (
                    <Grid item xs={12}>
                      <Box mt={2} display="flex" justifyContent="center">
                        <Button
                          variant="contained"
                          color="primary"
                          onClick={handleGenerateTests}
                          disabled={isGeneratingTests}
                          startIcon={isGeneratingTests ? <CircularProgress size={20} /> : null}
                        >
                          {isGeneratingTests ? 'Generating...' : 'Generate Tests from Response'}
                        </Button>
                      </Box>
                    </Grid>
                  )}
                </Grid>
              </TabPanel>

              {/* Response panel */}
              {currentResponse && (
                <Paper className={classes.responsePanel}>
                  <Box p={2}>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                      <Box>
                        <Typography variant="h6" component="span">
                          Response
                          <span
                            className={(() => {
                              const status = currentResponse.status;
                              if (status >= 200 && status < 300) {
                                return classes.statusSuccess;
                              }
                              if (status >= 400) {
                                return classes.statusError;
                              }
                              if (status >= 300) {
                                return classes.statusWarning;
                              }
                              return classes.statusInfo;
                            })()}
                          >
                            {` ${currentResponse.status} ${currentResponse.statusText}`}
                          </span>
                        </Typography>
                      </Box>
                      <Box>
                        <Chip
                          label={`${currentResponse.time}ms`}
                          size="small"
                          variant="outlined"
                          className={classes.responseTime}
                        />
                        <Chip
                          label={`${currentResponse.size} bytes`}
                          size="small"
                          variant="outlined"
                          style={{ marginLeft: '8px' }}
                        />
                      </Box>
                    </Box>

                    <Box mb={2}>
                      <Typography variant="subtitle2" gutterBottom>
                        Response Summary
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6} md={3}>
                          <Paper variant="outlined" style={{ padding: '8px 16px' }}>
                            <Typography variant="caption" color="textSecondary">
                              Status
                            </Typography>
                            <Typography variant="body2" style={{ fontWeight: 'bold' }}>
                              {currentResponse.status} {currentResponse.statusText}
                            </Typography>
                          </Paper>
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                          <Paper variant="outlined" style={{ padding: '8px 16px' }}>
                            <Typography variant="caption" color="textSecondary">
                              Time
                            </Typography>
                            <Typography variant="body2" style={{ fontWeight: 'bold' }}>
                              {currentResponse.time}ms
                            </Typography>
                          </Paper>
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                          <Paper variant="outlined" style={{ padding: '8px 16px' }}>
                            <Typography variant="caption" color="textSecondary">
                              Size
                            </Typography>
                            <Typography variant="body2" style={{ fontWeight: 'bold' }}>
                              {currentResponse.size} bytes
                            </Typography>
                          </Paper>
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                          <Paper variant="outlined" style={{ padding: '8px 16px' }}>
                            <Typography variant="caption" color="textSecondary">
                              Content Type
                            </Typography>
                            <Typography variant="body2" style={{ fontWeight: 'bold' }}>
                              {currentResponse.headers['content-type'] || 'Unknown'}
                            </Typography>
                          </Paper>
                        </Grid>
                      </Grid>
                    </Box>

                    <Tabs
                      value={responseTabValue}
                      onChange={(_, newValue) => setResponseTabValue(newValue)}
                      indicatorColor="primary"
                      textColor="primary"
                      variant="scrollable"
                      scrollButtons="auto"
                      style={{ marginBottom: '16px' }}
                    >
                      <Tab label="Body" />
                      <Tab label="Headers" />
                      <Tab label="Cookies" />
                    </Tabs>

                    {/* Response Body Tab */}
                    <TabPanel value={responseTabValue} index={0}>
                      {currentResponse.error ? (
                        <Alert severity="error">{currentResponse.error}</Alert>
                      ) : (
                        <Box>
                          <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                            <Typography variant="subtitle2">
                              Response Body
                            </Typography>
                            <FormControl variant="outlined" size="small" style={{ minWidth: '150px' }}>
                              <Select
                                value="pretty"
                                onChange={() => {}}
                              >
                                <MenuItem value="pretty">Pretty</MenuItem>
                                <MenuItem value="raw">Raw</MenuItem>
                                <MenuItem value="preview">Preview</MenuItem>
                              </Select>
                            </FormControl>
                          </Box>
                          <Paper variant="outlined" style={{ padding: '8px' }}>
                            <CodeSnippet
                              text={currentResponse.body}
                              language="json"
                              showCopyCodeButton
                            />
                          </Paper>
                        </Box>
                      )}
                    </TabPanel>

                    {/* Response Headers Tab */}
                    <TabPanel value={responseTabValue} index={1}>
                      <Box>
                        <Typography variant="subtitle2" gutterBottom>
                          Response Headers
                        </Typography>
                        <Paper variant="outlined" style={{ padding: '8px' }}>
                          <Table size="small" aria-label="response headers table">
                            <TableHead>
                              <TableRow>
                                <TableCell><Typography variant="subtitle2">Header</Typography></TableCell>
                                <TableCell><Typography variant="subtitle2">Value</Typography></TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {Object.entries(currentResponse.headers).map(([key, value]) => (
                                <TableRow key={key}>
                                  <TableCell component="th" scope="row">
                                    <Typography variant="body2" style={{ fontFamily: 'monospace' }}>
                                      {key}
                                    </Typography>
                                  </TableCell>
                                  <TableCell>
                                    <Typography variant="body2" style={{ fontFamily: 'monospace', wordBreak: 'break-all' }}>
                                      {value}
                                    </Typography>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </Paper>
                      </Box>
                    </TabPanel>

                    {/* Cookies Tab */}
                    <TabPanel value={responseTabValue} index={2}>
                      <Box>
                        <Typography variant="subtitle2" gutterBottom>
                          Cookies
                        </Typography>
                        {Object.keys(currentResponse.headers).some(h => h.toLowerCase() === 'set-cookie') ? (
                          <Paper variant="outlined" style={{ padding: '8px' }}>
                            <Table size="small" aria-label="cookies table">
                              <TableHead>
                                <TableRow>
                                  <TableCell><Typography variant="subtitle2">Name</Typography></TableCell>
                                  <TableCell><Typography variant="subtitle2">Value</Typography></TableCell>
                                  <TableCell><Typography variant="subtitle2">Domain</Typography></TableCell>
                                  <TableCell><Typography variant="subtitle2">Path</Typography></TableCell>
                                  <TableCell><Typography variant="subtitle2">Expires</Typography></TableCell>
                                </TableRow>
                              </TableHead>
                              <TableBody>
                                {/* We would parse cookies here in a real implementation */}
                                <TableRow>
                                  <TableCell colSpan={5}>
                                    <Typography variant="body2" align="center">
                                      Cookie parsing not implemented in this demo
                                    </Typography>
                                  </TableCell>
                                </TableRow>
                              </TableBody>
                            </Table>
                          </Paper>
                        ) : (
                          <EmptyState
                            missing="data"
                            title="No Cookies"
                            description="No cookies were returned in this response"
                          />
                        )}
                      </Box>
                    </TabPanel>
                  </Box>
                </Paper>
              )}
            </Paper>
          </Grid>
        </Grid>
      </Grid>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* Import Dialog */}
      <ImportDialog
        open={importDialogOpen}
        onClose={handleCloseImportDialog}
        onImportCollection={handleImportCollection}
        onImportEnvironment={handleImportEnvironment}
      />

      {/* Export Dialog */}
      <ExportDialog
        open={exportDialogOpen}
        onClose={handleCloseExportDialog}
        collections={collections}
        environments={environments}
      />

      {/* Edit Collection Dialog */}
      <Dialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        aria-labelledby="edit-collection-dialog-title"
      >
        <DialogTitle id="edit-collection-dialog-title">Edit Collection</DialogTitle>
        <DialogContent>
          <TextField
            margin="dense"
            id="collection-name"
            label="Collection Name"
            type="text"
            fullWidth
            value={editCollectionName}
            onChange={(e) => setEditCollectionName(e.target.value)}
          />
          <TextField
            margin="dense"
            id="collection-description"
            label="Description"
            type="text"
            fullWidth
            multiline
            minRows={4}
            value={editCollectionDescription}
            onChange={(e) => setEditCollectionDescription(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)} color="default">
            Cancel
          </Button>
          <Button onClick={handleSaveEditedCollection} color="primary">
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {/* Create Folder Dialog */}
      <CreateFolderDialog
        open={createFolderDialogOpen}
        onClose={() => setCreateFolderDialogOpen(false)}
        onCreateFolder={handleCreateFolder}
        collections={collections}
        selectedCollectionId={selectedCollectionForAction}
        selectedFolderId={selectedFolderForAction}
      />

      {/* Create Request Dialog */}
      <CreateRequestDialog
        open={createRequestDialogOpen}
        onClose={() => setCreateRequestDialogOpen(false)}
        onCreateRequest={handleCreateRequest}
        collections={collections}
        selectedCollectionId={selectedCollectionForAction}
        selectedFolderId={selectedFolderForAction}
      />

      {/* Rename Dialog */}
      {renameDialogData && (
        <RenameDialog
          open={renameDialogOpen}
          onClose={() => setRenameDialogOpen(false)}
          onRename={handleRename}
          itemType={renameDialogData.itemType}
          currentName={renameDialogData.currentName}
        />
      )}
    </div>
  );
};
